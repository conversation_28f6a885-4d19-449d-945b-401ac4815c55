import React, { useState, useEffect } from "react";
import { Container } from "@/components/ui/container";
import { DepartmentForm } from "@/components/forms/dashboard/academics/departments/department-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useDepartment } from "@/context/department-context";
import { toast } from "sonner";

const CreateDepartments = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(!!id);
  const [departmentData, setDepartmentData] = useState(null);
  const { fetchDepartmentById } = useDepartment();

  useEffect(() => {
    const loadDepartmentData = async () => {
      if (id) {
        try {
          setLoading(true);
          const data = await fetchDepartmentById(id);
          setDepartmentData(data);
        } catch (error) {
          console.error("Error loading department:", error);
          toast.error("Failed to load department data");
          navigate("/dashboard/academics/departments");
        } finally {
          setLoading(false);
        }
      }
    };

    loadDepartmentData();
  }, []);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Department" : "Create New Department"}
        actions={[
          {
            label: "Back to Departments",
            href: "/dashboard/academics/departments",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Academics", href: "/dashboard/academics" },
          { label: "Departments", href: "/dashboard/academics/departments" },
          { label: id ? "Edit Department" : "Create Department" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <DepartmentForm editingId={id} initialData={departmentData} />
      )}
    </Container>
  );
};

export default CreateDepartments;
