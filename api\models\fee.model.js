import mongoose from "mongoose";

const FeeSchema = new mongoose.Schema(
  {
    // Basic Fee Information
    feeType: {
      type: String,
      required: [true, "Fee type is required"],
      trim: true,
    },
    feeName: {
      type: String,
      required: [true, "Fee name is required"],
      trim: true,
    },
    feeCode: {
      type: String,
      required: [true, "Fee code is required"],
      trim: true,
      unique: true,
    },
    amount: {
      type: Number,
      required: [true, "Amount is required"],
      min: [0, "Amount must be positive"],
    },
    description: {
      type: String,
      trim: true,
    },

    // Academic Information
    class: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Class",
      required: [true, "Class is required"],
    },
    term: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Term",
      required: [true, "Term is required"],
    },

    // Payment Information
    dueDate: {
      type: Date,
      required: [true, "Due date is required"],
    },
    lateFeeAmount: {
      type: Number,
      min: [0, "Late fee amount must be positive"],
    },
    lateFeeGracePeriod: {
      type: Number,
      min: [0, "Grace period must be positive"],
    },

    // Administrative
    createdBy: {
      type: String,
      trim: true,
    },
    approvedBy: {
      type: String,
      trim: true,
    },
    effectiveDate: {
      type: Date,
    },
    expiryDate: {
      type: Date,
    },
    notes: {
      type: String,
      trim: true,
    },

    // Relationships
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },

    // Status
    status: {
      type: String,
      enum: ["active", "inactive"],
      default: "active",
    },
  },
  { timestamps: true }
);

export default mongoose.model("Fee", FeeSchema);
