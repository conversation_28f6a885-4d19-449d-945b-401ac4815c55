import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CheckCircle, XCircle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { useNavigate } from "react-router-dom";
import { ActionColumn } from "@/components/data-table/data-table-columns/action-column";
import { AlertDialog } from "@radix-ui/react-alert-dialog";
import {
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useTimetable } from "@/context/timetable-context";
import { useState } from "react";

export function TimetableHeader({
  data = {},
  action = true,
  timetables = [],
  selectedTimetableId,
  onTimetableSelect,
}) {
  const handleTimetableChange = (timetableId) => {
    if (onTimetableSelect) {
      onTimetableSelect(timetableId);
    }
  };

  const TimeTableActions = () => {
    const navigate = useNavigate();
    const { removeTimetable } = useTimetable();
    const [open, setOpen] = useState(false);

    const handleEdit = () => {
      navigate(`/dashboard/schedules/timetable/${data._id}/edit`);
    };

    const handleDelete = async () => {
      try {
        await removeTimetable(data._id);
        window.location.reload();
        toast.success("Time table has been deleted successfully. ");
        setOpen(false);
      } catch (error) {
        console.error("Failed to delete parent:", error);
        toast.error("Failed to delete parent. Please try again.");
      }
    };

    return (
      <>
        <ActionColumn onEdit={handleEdit} onDelete={() => setOpen(true)} />
        <AlertDialog open={open} onOpenChange={setOpen}>
          <AlertDialogTrigger asChild />
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Time Table?</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete this timetable? This action
                cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete}>
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </>
    );
  };

  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs">
      <Card className={`@container/card`}>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between gap-2">
                <CardTitle className="text-2xl font-semibold leading-tight">
                  {data?.name || "Untitled Program"}
                </CardTitle>
                {action && <TimeTableActions />}
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant={data.status === "active" ? "default" : "secondary"}
                  className={`w-fit ${
                    data.status === "active"
                      ? "bg-primary text-primary-foreground"
                      : "bg-secondary text-secondary-foreground"
                  }`}
                >
                  {data.status === "active" ? (
                    <CheckCircle className="h-3 w-3 mr-1" />
                  ) : (
                    <XCircle className="h-3 w-3 mr-1" />
                  )}
                  {data.status?.charAt(0).toUpperCase() + data.status?.slice(1)}
                </Badge>
              </div>
            </div>
            {timetables && timetables.length > 0 && (
              <div className="flex flex-col gap-2 min-w-0 sm:min-w-[240px]">
                <Label className="text-sm font-medium">Select Timetable</Label>
                <Select
                  value={selectedTimetableId || ""}
                  onValueChange={handleTimetableChange}
                >
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Choose a timetable" />
                  </SelectTrigger>
                  <SelectContent>
                    {timetables.map((timetable) => (
                      <SelectItem
                        key={timetable.id || timetable._id}
                        value={timetable.id || timetable._id}
                      >
                        {timetable.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>
    </div>
  );
}
