import express from "express";
import {
  createStudent,
  getAllStudents,
  getStudentById,
  updateStudent,
  deleteStudent,
  updateStudentStatus,
} from "../controllers/student.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createStudent
);

router.get("/", protect, authorize(["admin", "school-admin"]), getAllStudents);

router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  getStudentById
);

router.put(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  updateStudent
);

router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteStudent
);

router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateStudentStatus
);

export default router;
