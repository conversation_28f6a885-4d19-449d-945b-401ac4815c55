import React, { createContext, useContext, useState } from "react";
import {
  getAdminDashboardStats,
  getSchoolAdminDashboardStats,
  getStudentDashboardStats,
} from "@/api/dashboard-api";
import { toast } from "sonner";

const DashboardContext = createContext();

export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (!context) {
    throw new Error("useDashboard must be used within a DashboardProvider");
  }
  return context;
};

export const DashboardProvider = ({ children }) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Fetch admin dashboard statistics
  const fetchAdminDashboardStats = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getAdminDashboardStats();
      if (response.success) {
        setDashboardData(response.data);
      } else {
        throw new Error(response.message || "Failed to fetch dashboard data");
      }
    } catch (error) {
      console.error("Error fetching admin dashboard stats:", error);
      setError(error.message || "Failed to fetch dashboard statistics");
      toast.error("Failed to load dashboard data", {
        description: error.message || "Please try refreshing the page",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch school admin dashboard statistics
  const fetchSchoolAdminDashboardStats = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSchoolAdminDashboardStats();
      if (response.success) {
        setDashboardData(response.data);
      } else {
        throw new Error(response.message || "Failed to fetch dashboard data");
      }
    } catch (error) {
      console.error("Error fetching school admin dashboard stats:", error);
      setError(error.message || "Failed to fetch dashboard statistics");
      toast.error("Failed to load dashboard data", {
        description: error.message || "Please try refreshing the page",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch student dashboard statistics
  const fetchStudentDashboardStats = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getStudentDashboardStats();
      if (response.success) {
        setDashboardData(response.data);
      } else {
        throw new Error(response.message || "Failed to fetch dashboard data");
      }
    } catch (error) {
      console.error("Error fetching student dashboard stats:", error);
      setError(error.message || "Failed to fetch dashboard statistics");
      toast.error("Failed to load dashboard data", {
        description: error.message || "Please try refreshing the page",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh dashboard data
  const refreshDashboard = async (userRole) => {
    if (userRole === "admin") {
      await fetchAdminDashboardStats();
    } else if (userRole === "school-admin") {
      await fetchSchoolAdminDashboardStats();
    } else if (userRole === "student") {
      await fetchStudentDashboardStats();
    }
  };

  const value = {
    dashboardData,
    isLoading,
    error,
    fetchAdminDashboardStats,
    fetchSchoolAdminDashboardStats,
    fetchStudentDashboardStats,
    refreshDashboard,
  };

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
};
