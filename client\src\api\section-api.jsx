import axiosInstance from "./axios-instance";

export const createSection = async (sectionData) => {
  try {
    const response = await axiosInstance.post("/sections/create", sectionData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating section:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create section");
  }
};

export const getSections = async (filters = {}) => {
  try {
    const queryParams = new URLSearchParams();
    const queryString = queryParams.toString();
    const url = queryString ? `/sections?${queryString}` : "/sections";
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching sections:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch sections");
  }
};

export const getSectionById = async (id) => {
  try {
    const response = await axiosInstance.get(`/sections/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching section with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch section with ID ${id}`)
    );
  }
};

export const updateSection = async (id, sectionData) => {
  try {
    const response = await axiosInstance.put(`/sections/${id}`, sectionData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating section with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update section with ID ${id}`)
    );
  }
};

export const deleteSection = async (id) => {
  try {
    const response = await axiosInstance.delete(`/sections/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting section with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to delete section with ID ${id}`)
    );
  }
};

export const updateSectionStatus = async (id, isActive) => {
  try {
    const response = await axiosInstance.patch(`/sections/${id}/status`, {
      isActive,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating section status for section with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update section status for section with ID ${id}`)
    );
  }
};

export const getSectionsByClass = async (classId) => {
  try {
    const response = await axiosInstance.get(`/sections/class/${classId}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching sections for class ${classId}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch sections for class ${classId}`)
    );
  }
};
