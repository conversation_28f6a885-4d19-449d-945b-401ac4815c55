import React, { useState } from "react";
import { Plus, X, CircleHelp } from "lucide-react";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function MultiInput({
  form,
  name,
  label,
  placeholder = "Add item...",
  description,
  toolTipText,
  validation = {},
}) {
  const [inputValue, setInputValue] = useState("");

  const handleAdd = (fieldChange, currentValue = []) => {
    if (!inputValue.trim()) return;

    const updatedValue = [...(currentValue || []), inputValue.trim()];
    fieldChange(updatedValue);
    setInputValue("");
  };

  const handleRemove = (index, fieldChange, currentValue) => {
    const updatedValue = [...currentValue];
    updatedValue.splice(index, 1);
    fieldChange(updatedValue);
  };

  const handleKeyPress = (e, fieldChange, currentValue) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAdd(fieldChange, currentValue);
    }
  };

  return (
    <FormField
      control={form.control}
      name={name}
      rules={validation}
      render={({ field }) => (
        <FormItem>
          <FormLabel className="flex items-center gap-2">
            {label}
            {toolTipText && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0">
                      <CircleHelp className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">{toolTipText}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </FormLabel>
          <FormControl>
            <div className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder={placeholder}
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) =>
                    handleKeyPress(e, field.onChange, field.value)
                  }
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => handleAdd(field.onChange, field.value)}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {field.value?.length > 0 && (
                <div className="space-y-2">
                  {field.value.map((item, index) => (
                    <div
                      key={index}
                      className="flex items-center gap-2 rounded-md border bg-muted/50 p-2"
                    >
                      <span className="flex-1 text-sm">{item}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-muted-foreground hover:text-foreground"
                        onClick={() =>
                          handleRemove(index, field.onChange, field.value)
                        }
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
