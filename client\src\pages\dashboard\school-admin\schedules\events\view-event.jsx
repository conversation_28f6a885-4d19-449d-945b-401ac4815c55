import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { PageHeader } from "@/components/dashboard/page-header";
import { Container } from "@/components/ui/container";
import {
  Card,
  CardContent,
  CardHeader,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Calendar,
  MapPin,
  Users,
  User,
  AlertTriangle,
  Edit,
  Trash2,
  ArrowLeft,
  ExternalLink,
  PlayCircle,
  CheckCircle,
  XCircle,
  Phone,
  Mail,
  DollarSign,
  Clock3,
  Send,
} from "lucide-react";
import { toast } from "sonner";
import { useEvent } from "@/context/event-context";
import {
  getEventTypeIcon,
  getStatusIcon,
  getStatusBadgeVariant,
  getPriorityBadgeVariant,
} from "@/utils/get-functions";
import { formatDate, formatTimeAgo, formatTime } from "@/utils/formate-options";

const ViewEvent = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { currentEvent, isLoading, fetchEventById, removeEvent, updateStatus } =
    useEvent();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showStatusDialog, setShowStatusDialog] = useState(false);
  const [pendingStatus, setPendingStatus] = useState("");
  const [updatingStatus, setUpdatingStatus] = useState(false);

  useEffect(() => {
    if (id) {
      fetchEventById(id).catch((error) => {
        console.error("Error fetching event:", error);
        toast.error("Error", {
          description: "Failed to load event details",
        });
        navigate("/dashboard/schedules/events");
      });
    } else {
      navigate("/dashboard/schedules/events");
    }
  }, [id, navigate]);

  const handleStatusChange = async (newStatus) => {
    setUpdatingStatus(true);
    try {
      await updateStatus(id, newStatus);
      toast.success("Status updated successfully", {
        description: `Event status changed to ${newStatus.replace("_", " ")}`,
      });
      await fetchEventById(id);
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Status Update Failed", {
        description: "Failed to update event status",
      });
    }
    setUpdatingStatus(false);
    setShowStatusDialog(false);
  };

  const handleDelete = async () => {
    try {
      await removeEvent(id);
      toast.success("Event deleted successfully", {
        description: `${currentEvent.title} has been removed.`,
      });
      navigate("/dashboard/schedules/events");
    } catch (error) {
      console.error("Error deleting event:", error);
      toast.error("Delete Failed", {
        description: "Failed to delete event",
      });
    }
    setShowDeleteDialog(false);
  };

  const getStatusActions = () => {
    const status = currentEvent?.status;
    const actions = [];

    if (status !== "completed" && status !== "cancelled") {
      if (status !== "in_progress") {
        actions.push({
          label: "Mark as In Progress",
          status: "in_progress",
          icon: PlayCircle,
          variant: "outline",
        });
      }

      actions.push({
        label: "Mark as Complete",
        status: "completed",
        icon: CheckCircle,
        variant: "outline",
      });

      if (status !== "cancelled") {
        actions.push({
          label: "Cancel Event",
          status: "cancelled",
          icon: XCircle,
          variant: "outline",
        });
      }
    }

    return actions;
  };

  if (isLoading) {
    return <EventDetailSkeleton />;
  }

  if (!currentEvent) {
    return (
      <Container className="py-8">
        <PageHeader
          title="Event Not Found"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Schedule", href: "/dashboard/schedules" },
            { label: "Events", href: "/dashboard/schedules/events" },
            { label: "Not Found" },
          ]}
          actions={[
            {
              label: "Back to Events",
              icon: ArrowLeft,
              href: "/dashboard/schedules/events",
            },
          ]}
        />
        <Card className="py-0">
          <CardContent className="">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                <Calendar className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="mt-6 text-xl font-semibold text-foreground">
                Event not found
              </h3>
              <p className="mt-2 text-sm text-muted-foreground">
                The event you're looking for doesn't exist or has been removed.
              </p>
              <Button
                className="mt-6"
                onClick={() => navigate("/dashboard/schedules/events")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Events
              </Button>
            </div>
          </CardContent>
        </Card>
      </Container>
    );
  }

  return (
    <Container className="py-8">
      <div className="space-y-6">
        <PageHeader
          title="Event Details"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Schedule", href: "/dashboard/schedules" },
            { label: "Events", href: "/dashboard/schedules/events" },
            { label: currentEvent.title },
          ]}
          actions={[
            ...(currentEvent.status !== "completed" &&
            currentEvent.status !== "cancelled"
              ? [
                  {
                    label: "Edit",
                    icon: Edit,
                    href: `/dashboard/schedules/events/${currentEvent._id}/edit`,
                  },
                ]
              : []),
            {
              label: "Back to Events",
              icon: ArrowLeft,
              href: "/dashboard/schedules/events",
            },
          ]}
        />

        {/* Main Event Card */}
        <Card className="py-0">
          <CardHeader className="border-b bg-card px-6 py-6 rounded-t-md">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-muted border text-2xl">
                    {getEventTypeIcon(currentEvent.eventType)}
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <h1 className="text-2xl font-bold text-foreground mb-2">
                    {currentEvent.title}
                  </h1>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span className="font-medium capitalize">
                      {currentEvent.eventType.replace("_", " ")}
                    </span>
                    <span>•</span>
                    <span className="capitalize">
                      {currentEvent.attendeeType.replace("_", " ")}
                    </span>
                    <span>•</span>
                    <span>ID: {currentEvent._id}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Badge variant={getPriorityBadgeVariant(currentEvent.priority)}>
                  {currentEvent.priority} Priority
                </Badge>
                <Badge variant={getStatusBadgeVariant(currentEvent.status)}>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(currentEvent.status)}
                    <span className="capitalize">
                      {currentEvent.status.replace("_", " ")}
                    </span>
                  </div>
                </Badge>
              </div>
            </div>
          </CardHeader>

          <CardContent className="px-6 py-6">
            {/* Description Section */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-foreground mb-3">
                Event Description
              </h2>
              <div className="rounded-lg bg-muted p-4 border">
                <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                  {currentEvent.description}
                </p>
              </div>
            </div>

            {/* Additional Details */}
            {(currentEvent.instructions ||
              currentEvent.requirements ||
              currentEvent.materialsNeeded ||
              currentEvent.notes) && (
              <>
                <div className="mb-8 space-y-4">
                  {currentEvent.instructions && (
                    <div>
                      <h3 className="text-md font-semibold text-foreground mb-2">
                        Instructions
                      </h3>
                      <div className="rounded-lg bg-muted/50 p-4 border">
                        <p className="text-foreground whitespace-pre-wrap">
                          {currentEvent.instructions}
                        </p>
                      </div>
                    </div>
                  )}

                  {currentEvent.requirements && (
                    <div>
                      <h3 className="text-md font-semibold text-foreground mb-2">
                        Requirements
                      </h3>
                      <div className="rounded-lg bg-muted/50 p-4 border">
                        <p className="text-foreground whitespace-pre-wrap">
                          {currentEvent.requirements}
                        </p>
                      </div>
                    </div>
                  )}

                  {currentEvent.materialsNeeded && (
                    <div>
                      <h3 className="text-md font-semibold text-foreground mb-2">
                        Materials Needed
                      </h3>
                      <div className="rounded-lg bg-muted/50 p-4 border">
                        <p className="text-foreground whitespace-pre-wrap">
                          {currentEvent.materialsNeeded}
                        </p>
                      </div>
                    </div>
                  )}

                  {currentEvent.notes && (
                    <div>
                      <h3 className="text-md font-semibold text-foreground mb-2">
                        Additional Notes
                      </h3>
                      <div className="rounded-lg bg-muted/50 p-4 border">
                        <p className="text-foreground whitespace-pre-wrap">
                          {currentEvent.notes}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}

            <Separator className="my-6" />

            {/* Details Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Date & Time Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <Calendar className="mr-2 h-5 w-5 text-muted-foreground" />
                  Date & Time
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Start Date
                    </label>
                    <p className="text-foreground font-medium">
                      {formatDate(currentEvent.startDate)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      End Date
                    </label>
                    <p className="text-foreground font-medium">
                      {formatDate(currentEvent.endDate)}
                    </p>
                  </div>
                  {!currentEvent.isAllDayEvent ? (
                    <>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          Start Time
                        </label>
                        <p className="text-foreground font-medium">
                          {formatTime(currentEvent.startTime)}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">
                          End Time
                        </label>
                        <p className="text-foreground font-medium">
                          {formatTime(currentEvent.endTime)}
                        </p>
                      </div>
                    </>
                  ) : (
                    <div>
                      <Badge variant="outline">All Day Event</Badge>
                    </div>
                  )}
                </div>
              </div>

              {/* Venue & Location Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <MapPin className="mr-2 h-5 w-5 text-muted-foreground" />
                  Venue & Location
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Venue Type
                    </label>
                    <p className="text-foreground font-medium capitalize">
                      {currentEvent.venueType.replace("_", " ")}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Venue Name
                    </label>
                    <p className="text-foreground font-medium">
                      {currentEvent.venueName}
                    </p>
                  </div>
                  {currentEvent.roomNumber && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Room Number
                      </label>
                      <p className="text-foreground font-medium">
                        {currentEvent.roomNumber}
                      </p>
                    </div>
                  )}
                  {currentEvent.venueAddress && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Address
                      </label>
                      <p className="text-foreground font-medium">
                        {currentEvent.venueAddress}
                      </p>
                    </div>
                  )}
                  {currentEvent.onlineLink && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Online Link
                      </label>
                      <div className="mt-1">
                        <Button variant="outline" size="sm" asChild>
                          <a
                            href={currentEvent.onlineLink}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Join Event
                          </a>
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Attendee Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <Users className="mr-2 h-5 w-5 text-muted-foreground" />
                  Attendee Information
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Target Audience
                    </label>
                    <p className="text-foreground font-medium capitalize">
                      {currentEvent.attendeeType.replace("_", " ")}
                    </p>
                  </div>
                  {currentEvent.expectedAttendees && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Expected Attendees
                      </label>
                      <p className="text-foreground font-medium">
                        {currentEvent.expectedAttendees} people
                      </p>
                    </div>
                  )}
                  {currentEvent.capacity && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Venue Capacity
                      </label>
                      <p className="text-foreground font-medium">
                        {currentEvent.capacity} people
                      </p>
                    </div>
                  )}
                  {currentEvent.registrationRequired && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Registration
                      </label>
                      <div className="flex flex-col space-y-1">
                        <Badge variant="outline" className="w-fit">
                          Registration Required
                        </Badge>
                        {currentEvent.registrationDeadline && (
                          <p className="text-xs text-muted-foreground">
                            Deadline:{" "}
                            {formatDate(currentEvent.registrationDeadline)}
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                  {currentEvent.dresscode && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Dress Code
                      </label>
                      <p className="text-foreground font-medium">
                        {currentEvent.dresscode}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Contact & Budget Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <User className="mr-2 h-5 w-5 text-muted-foreground" />
                  Contact & Budget
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  {/* Organizer */}
                  {currentEvent.organizer && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Organizer
                      </label>
                      <p className="text-foreground font-medium">
                        {currentEvent.organizer.firstName}{" "}
                        {currentEvent.organizer.lastName}
                      </p>
                      {currentEvent.organizer.email && (
                        <p className="text-sm text-muted-foreground">
                          {currentEvent.organizer.email}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Coordinator */}
                  {currentEvent.coordinator && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Coordinator
                      </label>
                      <p className="text-foreground font-medium">
                        {currentEvent.coordinator.firstName}{" "}
                        {currentEvent.coordinator.lastName}
                      </p>
                      {currentEvent.coordinator.email && (
                        <p className="text-sm text-muted-foreground">
                          {currentEvent.coordinator.email}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Contact Person */}
                  {currentEvent.contactPerson && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Contact Person
                      </label>
                      <p className="text-foreground font-medium">
                        {currentEvent.contactPerson}
                      </p>
                      {currentEvent.contactPhone && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Phone className="h-3 w-3 mr-1" />
                          {currentEvent.contactPhone}
                        </div>
                      )}
                      {currentEvent.contactEmail && (
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Mail className="h-3 w-3 mr-1" />
                          {currentEvent.contactEmail}
                        </div>
                      )}
                    </div>
                  )}

                  {/* Budget */}
                  {currentEvent.budget && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Budget
                      </label>
                      <p className="text-foreground font-medium text-lg">
                        ${currentEvent.budget.toLocaleString()}
                      </p>
                    </div>
                  )}

                  {/* Tags */}
                  {currentEvent.tags && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Tags
                      </label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {currentEvent.tags.split(",").map((tag, index) => (
                          <Badge
                            key={index}
                            variant="secondary"
                            className="text-xs"
                          >
                            {tag.trim()}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <Separator className="my-6" />

            {/* Timeline Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-foreground flex items-center">
                <Clock3 className="mr-2 h-5 w-5 text-muted-foreground" />
                Event Timeline
              </h3>
              <div className="rounded-lg border bg-card p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Created
                    </label>
                    <p className="text-foreground font-medium">
                      {formatDate(currentEvent.createdAt)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatTimeAgo(currentEvent.createdAt)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Last Updated
                    </label>
                    <p className="text-foreground font-medium">
                      {formatDate(currentEvent.updatedAt)}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatTimeAgo(currentEvent.updatedAt)}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Current Status
                    </label>
                    <div className="flex items-center space-x-1">
                      <Badge
                        variant={getStatusBadgeVariant(currentEvent.status)}
                      >
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(currentEvent.status)}
                          <span className="capitalize">
                            {currentEvent.status.replace("_", " ")}
                          </span>
                        </div>
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>

          <CardFooter className="border-t bg-muted/50 px-6 py-4">
            <div className="flex w-full items-center justify-between">
              <Button
                variant="outline"
                onClick={() => navigate("/dashboard/schedules/events")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Events
              </Button>

              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  className="text-destructive border-destructive/20 hover:bg-destructive/10"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>

                {/* Status Change Buttons */}
                {getStatusActions().map((action, index) => (
                  <Button
                    key={index}
                    variant={action.variant}
                    className="text-primary border-primary/20 hover:bg-primary/10"
                    onClick={() => {
                      setPendingStatus(action.status);
                      setShowStatusDialog(true);
                    }}
                    disabled={updatingStatus}
                  >
                    <action.icon className="mr-2 h-4 w-4" />
                    {action.label}
                  </Button>
                ))}

                {currentEvent.status !== "completed" &&
                  currentEvent.status !== "cancelled" && (
                    <Button
                      onClick={() =>
                        navigate(
                          `/dashboard/schedules/events/${currentEvent._id}/edit`
                        )
                      }
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Event
                    </Button>
                  )}
              </div>
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
                <AlertTriangle className="h-5 w-5 text-destructive" />
              </div>
              <div>
                <AlertDialogTitle className="text-left">
                  Delete Event
                </AlertDialogTitle>
              </div>
            </div>
            <AlertDialogDescription className="text-left">
              Are you sure you want to delete "{currentEvent.title}"? This
              action cannot be undone and the event will be permanently removed
              from the system.
              {currentEvent.status === "completed" && (
                <span className="block mt-2 text-amber-600 font-medium">
                  Note: This event has already been completed.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive hover:bg-destructive/90"
            >
              Delete Event
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Status Change Confirmation Dialog */}
      <AlertDialog open={showStatusDialog} onOpenChange={setShowStatusDialog}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                <Send className="h-5 w-5 text-primary" />
              </div>
              <div>
                <AlertDialogTitle className="text-left">
                  Update Event Status
                </AlertDialogTitle>
              </div>
            </div>
            <AlertDialogDescription className="text-left">
              This will change the status of "{currentEvent.title}" to{" "}
              <strong className="capitalize">
                {pendingStatus.replace("_", " ")}
              </strong>
              . Are you sure you want to proceed?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleStatusChange(pendingStatus)}
              disabled={updatingStatus}
            >
              {updatingStatus ? "Updating..." : "Update Status"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Container>
  );
};

// Enhanced Skeleton Loading Component
const EventDetailSkeleton = () => {
  return (
    <Container className="py-8">
      <div className="space-y-6">
        {/* Header Skeleton */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="flex space-x-2">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-20" />
            </div>
          </div>
        </div>

        {/* Main Card Skeleton */}
        <Card>
          <CardHeader className="border-b bg-card px-6 py-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="space-y-3">
                  <Skeleton className="h-8 w-80" />
                  <Skeleton className="h-4 w-64" />
                </div>
              </div>
              <div className="flex space-x-3">
                <Skeleton className="h-6 w-24 rounded-full" />
                <Skeleton className="h-6 w-20 rounded-full" />
              </div>
            </div>
          </CardHeader>

          <CardContent className="px-6 py-6">
            {/* Description Section Skeleton */}
            <div className="mb-8 space-y-3">
              <Skeleton className="h-6 w-32" />
              <div className="rounded-lg bg-muted p-4 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </div>

            <Separator className="my-6" />

            {/* Details Grid Skeleton */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <Skeleton className="h-6 w-40" />
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-5 w-32" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-32" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-18" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <Skeleton className="h-6 w-36" />
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-5 w-28" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-5 w-40" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-48" />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <Skeleton className="h-6 w-44" />
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-28" />
                    <Skeleton className="h-5 w-32" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-5 w-28" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-6 w-32 rounded-full" />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <Skeleton className="h-6 w-36" />
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-4 w-48" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-5 w-36" />
                    <Skeleton className="h-4 w-44" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-28" />
                  </div>
                </div>
              </div>
            </div>

            <Separator className="my-6" />

            {/* Timeline Section Skeleton */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-32" />
              <div className="rounded-lg border bg-card p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-6 w-28 rounded-full" />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>

          <CardFooter className="border-t bg-muted/50 px-6 py-4">
            <div className="flex w-full items-center justify-between">
              <Skeleton className="h-10 w-32" />
              <div className="flex space-x-3">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-36" />
                <Skeleton className="h-10 w-28" />
                <Skeleton className="h-10 w-32" />
              </div>
            </div>
          </CardFooter>
        </Card>
      </div>
    </Container>
  );
};

export default ViewEvent;
