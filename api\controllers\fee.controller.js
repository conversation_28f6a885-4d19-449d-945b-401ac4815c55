import Fee from "../models/fee.model.js";

// Create a new fee
export const createFee = async (req, res) => {
  try {
    const feeData = req.body;
    // Ensure schoolId is set from user if not provided
    if (!feeData.schoolId && req.user?.schoolId) {
      feeData.schoolId = req.user.schoolId;
    }
    if (!feeData.schoolId) {
      return res.status(400).json({
        success: false,
        message: "School ID is required",
      });
    }
    const existingFee = await Fee.findOne({
      feeCode: feeData.feeCode,
      schoolId: feeData.schoolId,
    });
    if (existingFee) {
      return res.status(400).json({
        success: false,
        message: "A fee with this code already exists for this school",
      });
    }
    const newFee = new Fee(feeData);
    await newFee.save();
    res.status(201).json({
      success: true,
      message: "Fee created successfully",
      data: newFee,
    });
  } catch (error) {
    console.error("Create Fee Error:", error);
    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Fee code must be unique",
      });
    }
    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all fees
export const getAllFees = async (req, res) => {
  try {
    let query = {};
    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }
    if (req.query.schoolId && req.user?.role === "admin") {
      query.schoolId = req.query.schoolId;
    }
    const fees = await Fee.find(query)
      .populate("class", "name code")
      .populate("term", "name code")
      .populate("schoolId", "name")
      .sort({ createdAt: -1 });
    res.status(200).json({
      success: true,
      count: fees.length,
      data: fees,
    });
  } catch (error) {
    console.error("Get All Fees Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get fee by ID
export const getFeeById = async (req, res) => {
  try {
    const fee = await Fee.findById(req.params.id)
      .populate("class", "name code")
      .populate("term", "name code")
      .populate("schoolId", "name");
    if (!fee) {
      return res.status(404).json({
        success: false,
        message: "Fee not found",
      });
    }
    res.status(200).json({
      success: true,
      data: fee,
    });
  } catch (error) {
    console.error("Get Fee By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update fee
export const updateFee = async (req, res) => {
  try {
    const feeData = req.body;
    const feeId = req.params.id;
    if (!feeData.schoolId && req.user?.schoolId) {
      feeData.schoolId = req.user.schoolId;
    }
    const existingFee = await Fee.findById(feeId);
    if (!existingFee) {
      return res.status(404).json({
        success: false,
        message: "Fee not found",
      });
    }
    if (feeData.feeCode && feeData.feeCode !== existingFee.feeCode) {
      const codeExists = await Fee.findOne({
        feeCode: feeData.feeCode,
        schoolId: feeData.schoolId,
        _id: { $ne: feeId },
      });
      if (codeExists) {
        return res.status(400).json({
          success: false,
          message: "A fee with this code already exists for this school",
        });
      }
    }
    const fee = await Fee.findByIdAndUpdate(feeId, feeData, {
      new: true,
      runValidators: true,
    })
      .populate("class", "name code")
      .populate("term", "name code")
      .populate("schoolId", "name");
    if (!fee) {
      return res.status(404).json({
        success: false,
        message: "Fee not found",
      });
    }
    res.status(200).json({
      success: true,
      message: "Fee updated successfully",
      data: fee,
    });
  } catch (error) {
    console.error("Update Fee Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete fee
export const deleteFee = async (req, res) => {
  try {
    const fee = await Fee.findByIdAndDelete(req.params.id);
    if (!fee) {
      return res.status(404).json({
        success: false,
        message: "Fee not found",
      });
    }
    res.status(200).json({
      success: true,
      message: "Fee deleted successfully",
    });
  } catch (error) {
    console.error("Delete Fee Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update fee status
export const updateFeeStatus = async (req, res) => {
  try {
    const { status } = req.body;
    const fee = await Fee.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    );
    if (!fee) {
      return res.status(404).json({
        success: false,
        message: "Fee not found",
      });
    }
    res.status(200).json({
      success: true,
      message: "Fee status updated successfully",
      data: fee,
    });
  } catch (error) {
    console.error("Update Fee Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get fees by class
export const getFeesByClass = async (req, res) => {
  try {
    let query = { class: req.params.classId };
    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }
    if (req.query.schoolId && req.user?.role === "admin") {
      query.schoolId = req.query.schoolId;
    }
    const fees = await Fee.find(query)
      .populate("class", "name code")
      .populate("term", "name code")
      .populate("schoolId", "name");
    res.status(200).json({
      success: true,
      count: fees.length,
      data: fees,
    });
  } catch (error) {
    console.error("Get Fees By Class Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get fees by term
export const getFeesByTerm = async (req, res) => {
  try {
    let query = { term: req.params.termId };
    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }
    if (req.query.schoolId && req.user?.role === "admin") {
      query.schoolId = req.query.schoolId;
    }
    const fees = await Fee.find(query)
      .populate("class", "name code")
      .populate("term", "name code")
      .populate("schoolId", "name");
    res.status(200).json({
      success: true,
      count: fees.length,
      data: fees,
    });
  } catch (error) {
    console.error("Get Fees By Term Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
