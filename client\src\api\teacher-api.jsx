import axiosInstance from "./axios-instance";

export const createTeacher = async (teacherData) => {
  try {
    const response = await axiosInstance.post("/teachers/create", teacherData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating teacher:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create teacher");
  }
};

export const getTeachers = async () => {
  try {
    const response = await axiosInstance.get("/teachers");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching teachers:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch teachers");
  }
};

export const getTeacherById = async (id) => {
  try {
    const response = await axiosInstance.get(`/teachers/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching teacher with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch teacher with ID ${id}`)
    );
  }
};

export const getCurrentTeacherProfile = async () => {
  try {
    const response = await axiosInstance.get("/teachers/profile/me");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching current teacher profile:",
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error("Failed to fetch current teacher profile")
    );
  }
};

export const updateTeacher = async (id, teacherData) => {
  try {
    const response = await axiosInstance.put(`/teachers/${id}`, teacherData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating teacher with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update teacher with ID ${id}`)
    );
  }
};

export const deleteTeacher = async (id) => {
  try {
    const response = await axiosInstance.delete(`/teachers/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting teacher with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to delete teacher with ID ${id}`)
    );
  }
};

export const updateTeacherStatus = async (id, status) => {
  try {
    const response = await axiosInstance.patch(`/teachers/${id}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating teacher status for teacher with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update teacher status for teacher with ID ${id}`)
    );
  }
};

export const assignClasses = async (id, classIds) => {
  try {
    const response = await axiosInstance.patch(
      `/teachers/${id}/assign-classes`,
      {
        classIds,
      }
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error assigning classes to teacher with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to assign classes to teacher with ID ${id}`)
    );
  }
};
