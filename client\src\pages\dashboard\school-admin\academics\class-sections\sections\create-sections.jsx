import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { SectionForm } from "@/components/forms/dashboard/academics/sections/section-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useSection } from "@/context/section-context";

const CreateSections = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(!!id);
  const [sectionData, setSectionData] = useState(null);
  const { fetchSectionById } = useSection();

  useEffect(() => {
    const loadSectionData = async () => {
      if (id) {
        try {
          setLoading(true);
          const data = await fetchSectionById(id);
          setSectionData(data);
        } catch (error) {
          console.error("Error loading section:", error);
          toast.error("Failed to load section data");
          navigate("/dashboard/academics/sections");
        } finally {
          setLoading(false);
        }
      }
    };

    loadSectionData();
  }, []);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Section" : "Create New Section"}
        actions={[
          {
            label: "Back to Class Sections",
            href: "/dashboard/academics/classes",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Academics", href: "/dashboard/academics" },
          { label: "Class Sections", href: "/dashboard/academics/classes" },
          { label: id ? "Edit Section" : "Create Section" },
        ]}
      />

      {id && loading ? (
        <FormCardSkeleton />
      ) : (
        <SectionForm editingId={id} initialData={sectionData} />
      )}
    </Container>
  );
};

export default CreateSections;
