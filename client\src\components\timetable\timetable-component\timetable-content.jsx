import React from "react";
import { TimetableSkeleton } from "@/components/timetable/timetable-skeleton/timetable-skeleton";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, BookOpen, Users, CalendarDays } from "lucide-react";
import { formatTime } from "@/utils/formate-options";
import { generateTimeSlots, getCurrentDayKey } from "@/utils/helper-functions";
import { Avatar } from "@radix-ui/react-avatar";
import { AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const WEEKDAYS = [
  { key: "monday", label: "Monday", short: "Mon" },
  { key: "tuesday", label: "Tuesday", short: "Tue" },
  { key: "wednesday", label: "Wednesday", short: "Wed" },
  { key: "thursday", label: "Thursday", short: "Thu" },
  { key: "friday", label: "Friday", short: "Fri" },
  { key: "saturday", label: "Saturday", short: "Sat" },
];

export function TimetableContent({
  data,
  teacherNames,
  subjectDetails,
  classDetails,
  sectionDetails,
  isLoading,
  mode = "default",
  showBreaks = true,
  showClassCounts = true,
  showTeacherInfo = true,
}) {
  if (isLoading) {
    return <TimetableSkeleton />;
  }
  if (!data || !data.timetable) {
    return (
      <Card className="border-dashed">
        <CardContent className="flex flex-col items-center justify-center p-12 text-center">
          <div className="rounded-full bg-muted p-4 mb-4">
            <CalendarDays className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No Timetable Available</h3>
          <p className="text-muted-foreground max-w-sm">
            The timetable will appear here once it's created and assigned.
          </p>
        </CardContent>
      </Card>
    );
  }
  const findClassForTimeSlot = (dayKey, timeSlot) => {
    const daySchedule = data.timetable[dayKey] || [];
    return daySchedule.find((period) => {
      const periodStart = period.startTime.split(":")[0];
      const slotStart = timeSlot.split(":")[0];
      return periodStart === slotStart;
    });
  };

  const timeSlots = generateTimeSlots();
  const currentDay = getCurrentDayKey();

  return (
    <div className="space-y-6">
      {/* Timetable Grid */}
      <Card className="p-0">
        <CardContent className="p-0">
          <div className="h-full overflow-x-auto">
            <div className="divide-x divide-border min-w-max h-full">
              {/* Header Row */}
              <div
                className="grid border-b bg-muted/30"
                style={{ gridTemplateColumns: "100px repeat(6, 1fr)" }}
              >
                <div className="p-4 font-semibold text-center border-r">
                  <Clock className="h-4 w-4 mx-auto mb-1" />
                  <span className="text-sm">Time</span>
                </div>
                {WEEKDAYS.map((day) => {
                  const isToday = currentDay === day.key;
                  const daySchedule = data.timetable[day.key] || [];
                  return (
                    <div
                      key={day.key}
                      className={`p-4 text-center border-r last:border-r-0 ${
                        isToday
                          ? "bg-primary/10 text-primary font-semibold"
                          : ""
                      }`}
                    >
                      <div className="font-semibold">{day.short}</div>
                      <div className="text-xs text-muted-foreground hidden sm:block">
                        {day.label}
                      </div>
                      {showClassCounts && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {daySchedule.length}{" "}
                          {daySchedule.length === 1 ? "class" : "classes"}
                        </div>
                      )}
                      {isToday && (
                        <Badge variant="default" className="text-xs mt-1">
                          Today
                        </Badge>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Time Slot Rows */}
              {timeSlots.map((slot, slotIndex) => (
                <div
                  key={slot.time24}
                  className="grid border-b last:border-b-0"
                  style={{ gridTemplateColumns: "100px repeat(6, 1fr)" }}
                >
                  {/* Time Column */}
                  <div className="p-3 border-r bg-muted/20 flex flex-col items-center justify-center">
                    <div className="font-medium text-sm">{slot.time12}</div>
                    <div className="text-xs text-muted-foreground">
                      {formatTime(
                        `${Number.parseInt(slot.time24.split(":")[0]) + 1}:00`
                      )}
                    </div>
                  </div>

                  {/* Day Columns */}
                  {WEEKDAYS.map((day) => {
                    const period = findClassForTimeSlot(day.key, slot.time24);
                    const isToday = currentDay === day.key;

                    return (
                      <div
                        key={`${day.key}-${slot.time24}`}
                        className={`p-2 border-r last:border-r-0 min-h-[80px] ${
                          isToday ? "bg-primary/5" : ""
                        } ${slotIndex % 2 === 0 ? "bg-muted/10" : ""}`}
                      >
                        {period ? (
                          <div className="h-full">
                            <div className="rounded-lg p-3 h-full flex flex-col justify-between shadow-sm border-l-4 bg-card hover:shadow-md transition-shadow border-primary">
                              <div>
                                <div className="flex items-start gap-2 mb-2">
                                  <BookOpen className="h-3 w-3 text-muted-foreground mt-1 flex-shrink-0" />
                                  <div className="min-w-0">
                                    <div className="font-medium text-sm leading-tight">
                                      {subjectDetails[period.subjectId]?.name ||
                                        "Unknown Subject"}
                                    </div>
                                    {subjectDetails[period.subjectId]?.code && (
                                      <Badge
                                        variant="outline"
                                        className="text-xs mt-1"
                                      >
                                        {subjectDetails[period.subjectId].code}
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                                {(classDetails || mode === "teacher") && (
                                  <div className="flex items-center gap-1 text-xs text-muted-foreground mb-1">
                                    <Users className="h-3 w-3 flex-shrink-0 mr-2" />
                                    <span className="truncate">
                                      {mode === "teacher"
                                        ? `${period.className || "Class"}${
                                            period.sectionName
                                              ? ` ${period.sectionName}`
                                              : ""
                                          }`
                                        : `${
                                            classDetails[data.classId?._id] ||
                                            data.classId?.name ||
                                            "Class"
                                          }${
                                            sectionDetails[data.sectionId?._id]
                                              ? ` ${
                                                  sectionDetails[
                                                    data.sectionId._id
                                                  ]
                                                }`
                                              : ""
                                          }${
                                            data.sectionId?.name
                                              ? ` ${data.sectionId.name}`
                                              : ""
                                          }`}
                                    </span>
                                  </div>
                                )}

                                {showTeacherInfo &&
                                  teacherNames[period.teacherId] && (
                                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                      <Avatar className="h-5 w-5">
                                        <AvatarImage
                                          src=""
                                          alt={
                                            teacherNames[period.teacherId] ||
                                            "Teacher"
                                          }
                                        />
                                        <AvatarFallback className="text-xs bg-muted">
                                          {teacherNames[period.teacherId]
                                            ?.split(" ")
                                            .map((n) => n[0])
                                            .join("")
                                            .toUpperCase() || "T"}
                                        </AvatarFallback>
                                      </Avatar>
                                      <span>
                                        {teacherNames[period.teacherId]}
                                      </span>
                                    </div>
                                  )}
                              </div>

                              <div className="text-xs text-muted-foreground mt-2 pt-2 border-t">
                                {formatTime(period.startTime)} -{" "}
                                {formatTime(period.endTime)}
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="h-full flex items-center justify-center">
                            <div className="w-2 h-2 rounded-full bg-muted opacity-30"></div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-muted"></div>
                <span>Free Period</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded bg-primary/20 border-l-2 border-primary"></div>
                <span>Scheduled Class</span>
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              Scroll horizontally to view all days
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
