import { createContext, useContext, useState } from "react";
import {
  createStudent,
  getStudents,
  getStudentById,
  updateStudent,
  deleteStudent,
  updateStudentStatus,
} from "@/api/student-api";

const StudentContext = createContext({
  students: [],
  currentStudent: null,
  isLoading: false,
  error: null,
  addStudent: () => {},
  fetchAllStudents: () => {},
  fetchStudentById: () => {},
  editStudent: () => {},
  removeStudent: () => {},
  updateStatus: () => {},
});

export const StudentProvider = ({ children }) => {
  const [students, setStudents] = useState([]);
  const [currentStudent, setCurrentStudent] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addStudent = async (studentData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createStudent(studentData);
      setStudents((prevStudents) => [...prevStudents, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create student");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllStudents = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getStudents();
      setStudents(response.data);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to fetch students");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchStudentById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getStudentById(id);
      setCurrentStudent(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch student with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editStudent = async (id, studentData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateStudent(id, studentData);
      setStudents(
        students.map((stu) => (stu._id === id ? response.data : stu))
      );
      if (currentStudent && currentStudent._id === id) {
        setCurrentStudent(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update student with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeStudent = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteStudent(id);
      setStudents(students.filter((stu) => stu._id !== id));
      if (currentStudent && currentStudent._id === id) {
        setCurrentStudent(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete student with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, status) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateStudentStatus(id, status);
      setStudents(
        students.map((stu) => (stu._id === id ? response.data : stu))
      );
      if (currentStudent && currentStudent._id === id) {
        setCurrentStudent(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(
        error.message || `Failed to update student status with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  // Create student options for select inputs
  const studentOptions = students.map((stu) => ({
    label: `${stu.firstName} ${stu.lastName}`,
    value: stu._id,
    id: stu._id,
    firstName: stu.firstName,
    lastName: stu.lastName,
    rollNumber: stu.rollNumber,
    class: stu.class,
    section: stu.section,
  }));

  return (
    <StudentContext.Provider
      value={{
        students,
        setStudents,
        currentStudent,
        isLoading,
        error,
        addStudent,
        fetchAllStudents,
        fetchStudentById,
        editStudent,
        removeStudent,
        updateStatus,
        studentOptions,
      }}
    >
      {children}
    </StudentContext.Provider>
  );
};

export const useStudent = () => useContext(StudentContext);
