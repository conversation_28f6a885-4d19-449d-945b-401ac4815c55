import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.jsx";
import { ThemeProvider } from "./context/theme-context";
import { AuthProvider } from "./context/auth-context.jsx";
import { ContactProvider } from "./context/contact-context";
import { SchoolProvider } from "./context/school-context";
import { SchoolAdminProvider } from "./context/school-admin-context";
import { NotificationProvider } from "./context/notification-context";
import { DashboardProvider } from "./context/dashboard-context";
import { ParentProvider } from "./context/parent-context";
import { DepartmentProvider } from "./context/department-context";
import { SubjectProvider } from "./context/subject-context";
import { TermProvider } from "./context/term-context";
import { TeacherProvider } from "./context/teacher-context";
import { SectionProvider } from "./context/section-context";
import { ClassProvider } from "./context/class-context";
import { StudentProvider } from "./context/student-context";
import { FeeProvider } from "./context/fee-context";
import { TimetableProvider } from "./context/timetable-context";
import { EventProvider } from "./context/event-context";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      <AuthProvider>
        <ContactProvider>
          <SchoolProvider>
            <SchoolAdminProvider>
              <NotificationProvider>
                <DashboardProvider>
                  <ParentProvider>
                    <DepartmentProvider>
                      <SubjectProvider>
                        <TermProvider>
                          <TeacherProvider>
                            <ClassProvider>
                              <SectionProvider>
                                <StudentProvider>
                                  <FeeProvider>
                                    <TimetableProvider>
                                      <EventProvider>
                                        <App />
                                      </EventProvider>
                                    </TimetableProvider>
                                  </FeeProvider>
                                </StudentProvider>
                              </SectionProvider>
                            </ClassProvider>
                          </TeacherProvider>
                        </TermProvider>
                      </SubjectProvider>
                    </DepartmentProvider>
                  </ParentProvider>
                </DashboardProvider>
              </NotificationProvider>
            </SchoolAdminProvider>
          </SchoolProvider>
        </ContactProvider>
      </AuthProvider>
    </ThemeProvider>
  </StrictMode>
);
