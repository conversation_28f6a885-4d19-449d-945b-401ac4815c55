import { useAuth } from "@/context/auth-context";
import { Navigate, useLocation } from "react-router-dom";

export const ProtectedRoute = ({
  children,
  allowedRoles = [],
  redirectPath = "/sign-in",
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return (
      <>
        <div className="flex justify-center items-center h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-y-2 border-primary"></div>
        </div>
      </>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};
