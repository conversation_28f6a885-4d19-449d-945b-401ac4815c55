import express from "express";
import {
  createSubject,
  getAllSubjects,
  getSubjectById,
  updateSubject,
  deleteSubject,
  updateSubjectStatus,
  getSubjectsByDepartment,
} from "../controllers/subject.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createSubject
);

router.get(
  "/",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getAllSubjects
);

router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getSubjectById
);

router.put(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  updateSubject
);

router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteSubject
);

router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateSubjectStatus
);

router.get(
  "/department/:departmentId",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getSubjectsByDepartment
);

export default router;
