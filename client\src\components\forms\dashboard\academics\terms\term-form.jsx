import React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { Calendar, FileText, GraduationCap } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { academicYears } from "@/utils/form-options";
import { DateInput } from "@/components/form-inputs/date-input";
import { SwitchInput } from "@/components/form-inputs/switch-input";
import { useTerm } from "@/context/term-context";

export function TermForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { addTerm, editTerm } = useTerm();

  const form = useForm({
    defaultValues: {
      // General details
      name: initialData?.name || "",
      code: initialData?.code || "",
      description: initialData?.description || "",

      // Academic details
      academicYear: initialData?.academicYear || "",
      startDate: initialData?.startDate || "",
      endDate: initialData?.endDate || "",
      isActive: initialData?.isActive || true,

      // Additional information
      notes: initialData?.notes || "",
    },
  });

  const handleSubmit = async (data) => {
    try {
      if (editingId) {
        await editTerm(editingId, data);
        toast.success("Term updated successfully", {
          description: `${data.name} has been updated.`,
        });
        navigate("/dashboard/academics/terms");
      } else {
        await addTerm(data);
        toast.success("Term created successfully", {
          description: `${data.name} has been added.`,
        });
      }
    } catch (error) {
      console.error("Term form error:", error);
      toast.error(
        error.response?.data?.message || error.message || "Submission failed."
      );
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormCard title="Term Details" icon={Calendar}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <TextInput
                form={form}
                name="name"
                label="Term Name"
                placeholder="e.g., Fall 2025"
                validation={{ required: "Term name is required" }}
              />

              <TextInput
                form={form}
                name="code"
                label="Term Code"
                placeholder="Enter term code"
                validation={{ required: "Term code is required" }}
              />

              <div className="md:col-span-2">
                <TextareaInput
                  form={form}
                  name="description"
                  label="Description"
                  placeholder="Enter a brief description of the term..."
                  inputProps={{ rows: 3 }}
                />
              </div>
            </div>
          </FormCard>

          <FormCard title="Academic Details" icon={GraduationCap}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              <SelectInput
                form={form}
                name="academicYear"
                label="Academic Year"
                options={academicYears}
                placeholder="Select academic year"
                validation={{ required: "Academic year is required" }}
              />
              <DateInput
                form={form}
                name="endDate"
                label="End Date"
                placeholder="Select end date"
                validation={{ required: "End date is required" }}
              />
              <DateInput
                form={form}
                name="startDate"
                label="Start Date"
                placeholder="Select start date"
                validation={{ required: "Start date is required" }}
              />
              <div className="lg:col-span-3">
                <SwitchInput form={form} name="isActive" label="Is Active?" />
              </div>
            </div>
          </FormCard>

          <FormCard title="Additional Information" icon={FileText}>
            <TextareaInput
              form={form}
              name="notes"
              label="Notes"
              placeholder="Enter any additional information..."
              inputProps={{ rows: 3 }}
            />
          </FormCard>

          <FormFooter
            href="/dashboard/academics/terms"
            editingId={editingId}
            title="Term"
          />
        </form>
      </Form>
    </div>
  );
}
