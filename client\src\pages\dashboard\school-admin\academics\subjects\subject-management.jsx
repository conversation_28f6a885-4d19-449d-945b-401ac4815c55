import { useState, useEffect } from "react";
import { Menu } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetDescription,
  SheetTrigger,
} from "@/components/ui/sheet";
import { SubjectList } from "@/pages/dashboard/school-admin/academics/subjects/subject-list";
import { SubjectDetails } from "@/pages/dashboard/school-admin/academics/subjects/subject-details";
import { useSubject } from "@/context/subject-context";
import { toast } from "sonner";

export default function SubjectManagement() {
  const [subjects, setSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const { fetchAllSubjects } = useSubject();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await fetchAllSubjects();
        setSubjects(response.data || []);
      } catch (error) {
        console.error("Error fetching subjects:", error);
        toast.error("Error", {
          description: error.message || "Failed to fetch subjects",
        });
        setSubjects([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredSubjects =
    subjects?.filter(
      (subject) =>
        subject?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        subject?.code?.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  const handleSelectSubject = (subject) => {
    setSelectedSubject(subject);
    setIsSheetOpen(false);
  };

  const handleSubjectDeleted = (deletedSubjectId) => {
    setSubjects((prev) =>
      prev.filter((subject) => subject._id !== deletedSubjectId)
    );

    if (selectedSubject?._id === deletedSubjectId) {
      setSelectedSubject(null);
    }
  };

  return (
    <div className="flex h-full w-full relative">
      {/* Mobile Sheet */}
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button
            size="icon"
            className="md:hidden absolute top-6 right-4 z-50"
            aria-label="Open menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80 p-4">
          <SheetHeader>
            <SheetTitle>Subject Navigation</SheetTitle>
            <SheetDescription>Browse and manage subjects</SheetDescription>
          </SheetHeader>
          <SubjectList
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            subjects={filteredSubjects}
            selectedSubject={selectedSubject}
            onSelect={handleSelectSubject}
            onSubjectDeleted={handleSubjectDeleted}
            isLoading={isLoading}
            isMobile
          />
        </SheetContent>
      </Sheet>

      {/* Sidebar (Desktop) */}
      <SubjectList
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        subjects={filteredSubjects}
        selectedSubject={selectedSubject}
        onSelect={handleSelectSubject}
        onSubjectDeleted={handleSubjectDeleted}
        isLoading={isLoading}
      />

      {/* Main Content */}
      <div className="flex-1 h-full overflow-hidden">
        <SubjectDetails
          selectedSubject={selectedSubject}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
