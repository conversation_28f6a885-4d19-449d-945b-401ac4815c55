import { formatTime } from "@/utils/formate-options";

// Get the current day
export function getCurrentDayKey() {
  const dayKeys = [
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
  ];
  const today = new Date();
  return dayKeys[today.getDay()];
}

// Generate time slots from 8:00 AM to 6:00 PM
export function generateTimeSlots() {
  const slots = [];
  for (let hour = 8; hour <= 17; hour++) {
    const time24 = `${hour.toString().padStart(2, "0")}:00`;
    const time12 = formatTime(time24);
    slots.push({
      time24,
      time12,
      label: `${time12} - ${formatTime(`${hour + 1}:00`)}`,
    });
  }
  return slots;
}
