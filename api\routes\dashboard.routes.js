import express from "express";
import {
  getAdminDashboardStats,
  getSchoolAdminDashboardStats,
  getAdminChartData,
  getStudentDashboardStats,
  getSchoolAdminChartData,
} from "../controllers/dashboard.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.get(
  "/admin/stats",
  protect,
  authorize(["admin"]),
  getAdminDashboardStats
);

router.get(
  "/school-admin/stats",
  protect,
  authorize(["school-admin"]),
  getSchoolAdminDashboardStats
);

router.get(
  "/admin/chart-data",
  protect,
  authorize(["admin"]),
  getAdminChartData
);

router.get(
  "/school-admin/chart-data",
  protect,
  authorize(["school-admin"]),
  getSchoolAdminChartData
);

router.get(
  "/student/stats",
  protect,
  authorize(["student"]),
  getStudentDashboardStats
);

export default router;
