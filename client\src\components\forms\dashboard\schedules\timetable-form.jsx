import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { Calendar, Clock, Plus, Trash2 } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Form } from "@/components/ui/form";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { TextInput } from "@/components/form-inputs/text-input";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import { useClass } from "@/context/class-context";
import { useSection } from "@/context/section-context";
import { useSubject } from "@/context/subject-context";
import { useTeacher } from "@/context/teacher-context";
import { useTimetable } from "@/context/timetable-context";
import { useTerm } from "@/context/term-context";
import { SelectInput } from "@/components/form-inputs/select-input";
import { academicYears } from "@/utils/form-options";
import { useNavigate } from "react-router-dom";

const DAYS_OF_WEEK = [
  { key: "monday", label: "Monday" },
  { key: "tuesday", label: "Tuesday" },
  { key: "wednesday", label: "Wednesday" },
  { key: "thursday", label: "Thursday" },
  { key: "friday", label: "Friday" },
  { key: "saturday", label: "Saturday" },
];

export function TimetableForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { classOptions, fetchAllClasses } = useClass();
  const { sectionOptions, fetchAllSections } = useSection();
  const { subjectOptions, fetchAllSubjects } = useSubject();
  const { teacherOptions, fetchAllTeachers } = useTeacher();
  const { termOptions, fetchAllTerms } = useTerm();
  const { addTimetable, editTimetable } = useTimetable();

  const form = useForm({
    defaultValues: initialData || {
      name: "",
      academicYear: "",
      termId: "",
      classId: "",
      sectionId: "",
      morningBreak: "10:00",
      lunchBreak: "13:00",
      eveningBreak: "15:30",
      timetable: DAYS_OF_WEEK.reduce(
        (acc, day) => ({
          ...acc,
          [day.key]: [
            {
              subjectId: "",
              teacherId: "",
              startTime: "08:00",
              endTime: "09:00",
            },
          ],
        }),
        {}
      ),
    },
  });

  useEffect(() => {
    fetchAllClasses();
    fetchAllSubjects();
    fetchAllTeachers();
    fetchAllSections();
    fetchAllTerms();
  }, []);

  const handleSubmit = async (data) => {
    try {
      if (editingId) {
        await editTimetable(editingId, data);
        toast.success("Timetable updated successfully");
        navigate("/dashboard/schedules/timetable");
      } else {
        await addTimetable(data);
        toast.success("Timetable created successfully");
      }
    } catch (error) {
      console.error("Error submitting timetable form:", error);
      toast.error(
        error.message ||
          "Failed to save timetable information. Please try again."
      );
    }
  };

  return (
    <div className="pt-6">
      <FormCard title="Create Timetable" icon={Calendar}>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <TextInput
                form={form}
                name="name"
                label="Timetable Name"
                placeholder="e.g. Grade 10A - Science Term 1"
                validation={{
                  maxLength: {
                    value: 100,
                    message: "Name cannot exceed 100 characters",
                  },
                }}
              />
              <SelectInput
                form={form}
                name="academicYear"
                label="Academic Year"
                options={academicYears}
                placeholder="Select academic year"
                validation={{ required: "Academic year is required" }}
              />
              <ComboboxInput
                form={form}
                name="termId"
                label="Term"
                placeholder="Select term"
                options={termOptions}
                validation={{ required: "Term is required" }}
                href="/dashboard/academics/terms"
                toolTipText="Create a new term"
              />
            </div>

            {/* Class, Section, and Term Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <ComboboxInput
                form={form}
                name="classId"
                label="Class"
                placeholder="Select class"
                options={classOptions}
                validation={{ required: "Class is required" }}
                href="/dashboard/academics/classes"
                toolTipText="Create a new class"
              />

              <ComboboxInput
                form={form}
                name="sectionId"
                label="Section"
                placeholder="Select section"
                options={sectionOptions}
                validation={{ required: "Section is required" }}
                href="/dashboard/academics/classes"
                toolTipText="Create a new section"
              />
            </div>

            {/* Break Times */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <TextInput
                form={form}
                name="morningBreak"
                label="Morning Break"
                type="time"
                validation={{
                  required: "Morning break is required",
                }}
              />
              <TextInput
                form={form}
                name="lunchBreak"
                label="Lunch Break"
                type="time"
                validation={{
                  required: "Lunch break is required",
                }}
              />
              <TextInput
                form={form}
                name="eveningBreak"
                label="Evening Break"
                type="time"
                validation={{
                  required: "Evening break is required",
                }}
              />
            </div>

            <FormCard title="Week Days Timetable" icon={Clock}>
              <Tabs defaultValue={DAYS_OF_WEEK[0]?.key} className="w-full">
                <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 h-auto">
                  {DAYS_OF_WEEK.map((day) => (
                    <TabsTrigger
                      key={day.key}
                      value={day.key}
                      className="flex flex-col items-center gap-1"
                    >
                      <span className="text-xs">{day.label}</span>
                    </TabsTrigger>
                  ))}
                </TabsList>

                {DAYS_OF_WEEK.map((day) => (
                  <TabsContent key={day.key} value={day.key} className="mt-4">
                    <div className="space-y-4">
                      {/* Period entries */}
                      {form.watch(`timetable.${day.key}`)?.map((_, index) => (
                        <div
                          key={index}
                          className="relative border rounded-lg p-4"
                        >
                          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                            <ComboboxInput
                              form={form}
                              name={`timetable.${day.key}.${index}.subjectId`}
                              label="Subject"
                              placeholder="Select subject"
                              options={subjectOptions}
                              validation={{ required: "Subject is required" }}
                            />
                            <ComboboxInput
                              form={form}
                              name={`timetable.${day.key}.${index}.teacherId`}
                              label="Teacher"
                              placeholder="Select teacher"
                              options={teacherOptions}
                              validation={{ required: "Teacher is required" }}
                            />
                            <div>
                              <div className="grid grid-cols-2 gap-2">
                                <TextInput
                                  form={form}
                                  name={`timetable.${day.key}.${index}.startTime`}
                                  label="Start Time"
                                  type="time"
                                  validation={{
                                    required: "Start time is required",
                                    validate: (value) => {
                                      const endTime = form.getValues(
                                        `timetable.${day.key}.${index}.endTime`
                                      );
                                      if (endTime && value >= endTime) {
                                        return "Start time must be before end time";
                                      }
                                      return true;
                                    },
                                  }}
                                />
                                <TextInput
                                  form={form}
                                  name={`timetable.${day.key}.${index}.endTime`}
                                  label="End Time"
                                  type="time"
                                  validation={{
                                    required: "End time is required",
                                    validate: (value) => {
                                      const startTime = form.getValues(
                                        `timetable.${day.key}.${index}.startTime`
                                      );
                                      if (startTime && value <= startTime) {
                                        return "End time must be after start time";
                                      }
                                      return true;
                                    },
                                  }}
                                />
                              </div>
                            </div>
                          </div>

                          {/* Delete period button */}
                          {form.watch(`timetable.${day.key}`).length > 1 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground hover:bg-destructive/90 rounded-full"
                              onClick={() => {
                                const currentEntries = form.getValues(
                                  `timetable.${day.key}`
                                );
                                form.setValue(
                                  `timetable.${day.key}`,
                                  currentEntries.filter((_, i) => i !== index)
                                );
                              }}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      ))}

                      {/* Add new period button */}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="w-full"
                        onClick={() => {
                          const currentEntries =
                            form.getValues(`timetable.${day.key}`) || [];
                          form.setValue(`timetable.${day.key}`, [
                            ...currentEntries,
                            {
                              subjectId: "",
                              teacherId: "",
                              startTime: "08:00",
                              endTime: "09:00",
                            },
                          ]);
                        }}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Add Period
                      </Button>
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </FormCard>

            <FormFooter
              href="/timetable"
              parent="schedules"
              title="Timetable"
              editingId={editingId}
            />
          </form>
        </Form>
      </FormCard>
    </div>
  );
}

export default TimetableForm;
