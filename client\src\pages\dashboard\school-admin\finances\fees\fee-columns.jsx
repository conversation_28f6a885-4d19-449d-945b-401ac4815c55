import React, { useState } from "react";
import { StatusColumn } from "@/components/data-table/data-table-columns/status-column";
import { DateColumn } from "@/components/data-table/data-table-columns/data-columns";
import { SortableColumn } from "@/components/data-table/data-table-columns/sortable-column";
import { ActionColumn } from "@/components/data-table/data-table-columns/action-column";
import { useNavigate } from "react-router-dom";
import { useFee } from "@/context/fee-context";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

export const FeeColumns = () => [
  {
    accessorKey: "feeName",
    header: ({ column }) => <SortableColumn column={column} title="Fee Name" />,
    cell: ({ row }) => (
      <div className="font-medium">{row.original.feeName}</div>
    ),
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: "feeCode",
    header: ({ column }) => <SortableColumn column={column} title="Fee Code" />,
    cell: ({ row }) => <div className="text-sm">{row.original.feeCode}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "feeType",
    header: ({ column }) => <SortableColumn column={column} title="Type" />,
    cell: ({ row }) => <div className="text-sm">{row.original.feeType}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "amount",
    header: ({ column }) => (
      <SortableColumn column={column} title="Amount (₹)" />
    ),
    cell: ({ row }) => <div className="text-sm">₹{row.original.amount}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "class",
    header: ({ column }) => <SortableColumn column={column} title="Class" />,
    cell: ({ row }) => (
      <div className="text-sm">{row.original.class?.name || "-"}</div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "term",
    header: ({ column }) => <SortableColumn column={column} title="Term" />,
    cell: ({ row }) => (
      <div className="text-sm">{row.original.term?.name || "-"}</div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "dueDate",
    header: ({ column }) => <SortableColumn column={column} title="Due Date" />,
    cell: ({ row }) => {
      return (
        <div className="text-sm">
          {new Date(row.original.dueDate).toLocaleDateString()}
        </div>
      );
    },
    enableSorting: true,
    sortingFn: "datetime",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => (
      <StatusColumn
        row={row}
        statusField="status"
        variant={{
          active: "success",
          inactive: "destructive",
        }}
      />
    ),
    enableSorting: true,
  },
  {
    accessorKey: "createdAt",
    header: "Date Added",
    cell: ({ row }) => <DateColumn row={row} accessorKey="createdAt" />,
    enableSorting: true,
    sortingFn: "datetime",
  },
  {
    id: "actions",
    cell: ({ row }) => <FeeActions row={row} />,
  },
];

const FeeActions = ({ row }) => {
  const navigate = useNavigate();
  const { removeFee } = useFee();
  const [open, setOpen] = useState(false);

  const handleView = () => {
    navigate(`/dashboard/finances/fees/${row.original._id}`);
  };

  const handleEdit = () => {
    navigate(`/dashboard/finances/fees/${row.original._id}/edit`);
  };

  const handleDelete = async () => {
    try {
      await removeFee(row.original._id);
      toast.success(`${row.original.feeName} deleted successfully.`);
      setOpen(false);
    } catch (error) {
      console.error("Failed to delete fee:", error);
      toast.error("Failed to delete fee. Please try again.");
    }
  };

  return (
    <>
      <ActionColumn
        row={row}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={() => setOpen(true)}
      />
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogTrigger asChild />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {row.original.feeName}?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this fee? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
