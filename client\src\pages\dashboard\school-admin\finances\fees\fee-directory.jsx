import { useEffect } from "react";
import { useFee } from "@/context/fee-context";
import {
  CreditCard,
  BadgeDollarSign,
  PlusCircle,
  CalendarClock,
  BookOpen,
} from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { FeeColumns } from "@/pages/dashboard/school-admin/finances/fees/fee-columns";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";

const FeeDirectory = () => {
  const { fees, isLoading, fetchAllFees } = useFee();

  useEffect(() => {
    fetchAllFees();
  }, []);

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Fee Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Finances", href: "/dashboard/finances" },
            { label: "Fees" },
          ]}
          actions={[
            {
              label: "New Fee",
              icon: PlusCircle,
              href: "/dashboard/finances/fees/create",
            },
          ]}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Fees"
            value={fees.length}
            description="All registered fees"
            icon={CreditCard}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Active Fees"
            value={fees.filter((f) => f.status === "active").length}
            description="Currently active"
            icon={BadgeDollarSign}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Recently Added"
            value={
              fees.filter((fee) => {
                const createdAt = new Date(fee.createdAt);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return createdAt >= thirtyDaysAgo;
              }).length
            }
            description="Added in last 30 days"
            icon={CalendarClock}
            isLoading={isLoading}
          />

          <StatCard
            title="Top Class"
            value={(() => {
              if (fees.length === 0) return "N/A";
              const classCount = fees.reduce((acc, fee) => {
                const className = fee.class?.name || fee.class || "Unknown";
                acc[className] = (acc[className] || 0) + 1;
                return acc;
              }, {});
              let topClass = "Unknown";
              let max = 0;
              for (const [className, count] of Object.entries(classCount)) {
                if (count > max) {
                  topClass = className;
                  max = count;
                }
              }
              return topClass;
            })()}
            description="Most fees for class"
            icon={BookOpen}
            isLoading={isLoading}
          />
        </div>

        <div>
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable data={fees} columns={FeeColumns()} model="fee" />
          )}
        </div>
      </main>
    </div>
  );
};

export default FeeDirectory;
