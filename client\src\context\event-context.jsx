import { createContext, useContext, useState } from "react";
import {
  createEvent,
  getEvents,
  getEventById,
  updateEvent,
  deleteEvent,
  updateEventStatus,
  updateEventActiveStatus,
  getEventsByDateRange,
  getUpcomingEvents,
  getEventsByType,
} from "@/api/event-api";

const EventContext = createContext({
  events: [],
  currentEvent: null,
  upcomingEvents: [],
  isLoading: false,
  error: null,
  addEvent: () => {},
  fetchAllEvents: () => {},
  fetchEventById: () => {},
  editEvent: () => {},
  removeEvent: () => {},
  updateStatus: () => {},
  updateActiveStatus: () => {},
  fetchEventsByDateRange: () => {},
  fetchUpcomingEvents: () => {},
  fetchEventsByType: () => {},
});

export const EventProvider = ({ children }) => {
  const [events, setEvents] = useState([]);
  const [currentEvent, setCurrentEvent] = useState(null);
  const [upcomingEvents, setUpcomingEvents] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addEvent = async (eventData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createEvent(eventData);
      setEvents((prevEvents) => [...prevEvents, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create event");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllEvents = async (filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getEvents(filters);
      setEvents(response.data);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to fetch events");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchEventById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getEventById(id);
      setCurrentEvent(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch event with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editEvent = async (id, eventData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateEvent(id, eventData);
      setEvents(
        events.map((event) => (event._id === id ? response.data : event))
      );
      if (currentEvent && currentEvent._id === id) {
        setCurrentEvent(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update event with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeEvent = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteEvent(id);
      setEvents(events.filter((event) => event._id !== id));
      setUpcomingEvents(upcomingEvents.filter((event) => event._id !== id));
      if (currentEvent && currentEvent._id === id) {
        setCurrentEvent(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete event with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, status) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateEventStatus(id, status);
      setEvents(
        events.map((event) => (event._id === id ? response.data : event))
      );
      setUpcomingEvents(
        upcomingEvents.map((event) =>
          event._id === id ? response.data : event
        )
      );
      if (currentEvent && currentEvent._id === id) {
        setCurrentEvent(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update event status with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateActiveStatus = async (id, isActive) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateEventActiveStatus(id, isActive);
      setEvents(
        events.map((event) => (event._id === id ? response.data : event))
      );
      if (currentEvent && currentEvent._id === id) {
        setCurrentEvent(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(
        error.message || `Failed to update event active status with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const fetchEventsByDateRange = async (startDate, endDate) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getEventsByDateRange(startDate, endDate);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(
        error.message ||
          `Failed to fetch events for date range: ${startDate} to ${endDate}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const fetchUpcomingEvents = async (limit = 5) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getUpcomingEvents(limit);
      setUpcomingEvents(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || "Failed to fetch upcoming events");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchEventsByType = async (eventType) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getEventsByType(eventType);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch events of type: ${eventType}`);
      setIsLoading(false);
      throw error;
    }
  };

  // Create event options for select inputs
  const eventOptions = events.map((event) => ({
    label: `${event.title} - ${new Date(event.startDate).toLocaleDateString()}`,
    value: event._id,
    id: event._id,
    title: event.title,
    eventType: event.eventType,
    startDate: event.startDate,
    endDate: event.endDate,
    status: event.status,
    priority: event.priority,
  }));

  // Get events by status
  const getEventsByStatus = (status) => {
    return events.filter((event) => event.status === status);
  };

  // Get events by priority
  const getEventsByPriority = (priority) => {
    return events.filter((event) => event.priority === priority);
  };

  // Get today's events
  const getTodaysEvents = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    return events.filter((event) => {
      const eventDate = new Date(event.startDate);
      eventDate.setHours(0, 0, 0, 0);
      return eventDate >= today && eventDate < tomorrow;
    });
  };

  // Get this week's events
  const getThisWeeksEvents = () => {
    const today = new Date();
    const startOfWeek = new Date(
      today.setDate(today.getDate() - today.getDay())
    );
    startOfWeek.setHours(0, 0, 0, 0);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(endOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    return events.filter((event) => {
      const eventDate = new Date(event.startDate);
      return eventDate >= startOfWeek && eventDate <= endOfWeek;
    });
  };

  return (
    <EventContext.Provider
      value={{
        events,
        setEvents,
        currentEvent,
        setCurrentEvent,
        upcomingEvents,
        setUpcomingEvents,
        isLoading,
        error,
        addEvent,
        fetchAllEvents,
        fetchEventById,
        editEvent,
        removeEvent,
        updateStatus,
        updateActiveStatus,
        fetchEventsByDateRange,
        fetchUpcomingEvents,
        fetchEventsByType,
        eventOptions,
        getEventsByStatus,
        getEventsByPriority,
        getTodaysEvents,
        getThisWeeksEvents,
      }}
    >
      {children}
    </EventContext.Provider>
  );
};

export const useEvent = () => useContext(EventContext);
