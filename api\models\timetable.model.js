import mongoose from "mongoose";

const PeriodSchema = new mongoose.Schema({
  subjectId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Subject",
    required: [true, "Subject is required"],
  },
  teacherId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Teacher",
    required: [true, "Teacher is required"],
  },
  startTime: {
    type: String,
    required: [true, "Start time is required"],
    match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)"],
  },
  endTime: {
    type: String,
    required: [true, "End time is required"],
    match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, "Invalid time format (HH:MM)"],
  },
});

const DayScheduleSchema = new mongoose.Schema({
  monday: [PeriodSchema],
  tuesday: [PeriodSchema],
  wednesday: [PeriodSchema],
  thursday: [PeriodSchema],
  friday: [PeriodSchema],
  saturday: [PeriodSchema],
  sunday: [PeriodSchema],
});

const TimetableSchema = new mongoose.Schema(
  {
    // Basic Information
    name: {
      type: String,
      required: [true, "Timetable name is required"],
      trim: true,
      maxlength: [100, "Name cannot exceed 100 characters"],
    },
    classId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Class",
      required: [true, "Class is required"],
    },
    sectionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Section",
      required: [true, "Section is required"],
    },
    termId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Term",
      required: [true, "Term is required"],
    },
    academicYear: {
      type: String,
      required: [true, "Academic year is required"],
      trim: true,
    },

    // Break Times
    morningBreak: {
      type: String,
      required: [true, "Morning break time is required"],
      match: [
        /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
        "Invalid time format (HH:MM)",
      ],
    },
    lunchBreak: {
      type: String,
      required: [true, "Lunch break time is required"],
      match: [
        /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
        "Invalid time format (HH:MM)",
      ],
    },
    eveningBreak: {
      type: String,
      match: [
        /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
        "Invalid time format (HH:MM)",
      ],
    },

    // Weekly Schedule
    timetable: {
      type: DayScheduleSchema,
      required: true,
    },

    // Relationships
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },

    // Status
    status: {
      type: String,
      enum: ["active", "inactive"],
      default: "active",
    },

    // Metadata
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
  }
);

TimetableSchema.index({ schoolId: 1, classId: 1, sectionId: 1, termId: 1 });
TimetableSchema.index({ schoolId: 1, academicYear: 1 });
TimetableSchema.index({ schoolId: 1, status: 1 });

export default mongoose.model("Timetable", TimetableSchema);
