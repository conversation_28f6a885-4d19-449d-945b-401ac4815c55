import React, { useState, useEffect } from "react";
import { Container } from "@/components/ui/container";
import { ClassForm } from "@/components/forms/dashboard/academics/classes/class-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useClass } from "@/context/class-context";
import { toast } from "sonner";

const CreateClasses = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(!!id);
  const [classData, setClassData] = useState(null);
  const { fetchClassById } = useClass();

  useEffect(() => {
    const loadClassData = async () => {
      if (id) {
        try {
          setLoading(true);
          const data = await fetchClassById(id);
          setClassData(data);
        } catch (error) {
          console.error("Error loading class:", error);
          toast.error("Failed to load class data");
          navigate("/dashboard/academics/classes");
        } finally {
          setLoading(false);
        }
      }
    };

    loadClassData();
  }, []);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Class" : "Create New Class"}
        actions={[
          {
            label: "Back to Classes",
            href: "/dashboard/academics/classes",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Academics", href: "/dashboard/academics" },
          { label: "Classes", href: "/dashboard/academics/classes" },
          { label: id ? "Edit Class" : "Create Class" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <ClassForm editingId={id} initialData={classData} />
      )}
    </Container>
  );
};

export default CreateClasses;
