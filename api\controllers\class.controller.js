import Class from "../models/class.model.js";

// Create a new class
export const createClass = async (req, res) => {
  try {
    const classData = req.body;

    if (!classData.schoolId && req.user?.schoolId) {
      classData.schoolId = req.user.schoolId;
    }

    const newClass = new Class(classData);
    await newClass.save();

    res.status(201).json({
      success: true,
      message: "Class created successfully",
      data: newClass,
    });
  } catch (error) {
    console.error("Create Class Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Class code already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all classes
export const getAllClasses = async (req, res) => {
  try {
    const { schoolId, departmentId, academicYear, isActive } = req.query;

    let query = {};

    // Apply filters
    if (schoolId) {
      query.schoolId = schoolId;
    } else if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    if (departmentId) {
      query.department = departmentId;
    }

    if (academicYear) {
      query.academicYear = academicYear;
    }

    if (isActive !== undefined) {
      query.isActive = isActive === "true";
    }

    const classes = await Class.find(query)
      .sort({ createdAt: -1 })
      .populate("department", "name code")
      .populate("schoolId", "name");

    res.status(200).json({
      success: true,
      count: classes.length,
      data: classes,
    });
  } catch (error) {
    console.error("Get All Classes Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get class by ID
export const getClassById = async (req, res) => {
  try {
    const classItem = await Class.findById(req.params.id)
      .populate("department", "name code")
      .populate("schoolId", "name");

    if (!classItem) {
      return res.status(404).json({
        success: false,
        message: "Class not found",
      });
    }

    res.status(200).json({
      success: true,
      data: classItem,
    });
  } catch (error) {
    console.error("Get Class By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update class
export const updateClass = async (req, res) => {
  try {
    const classData = req.body;

    const classItem = await Class.findByIdAndUpdate(req.params.id, classData, {
      new: true,
      runValidators: true,
    }).populate("department", "name code");

    if (!classItem) {
      return res.status(404).json({
        success: false,
        message: "Class not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Class updated successfully",
      data: classItem,
    });
  } catch (error) {
    console.error("Update Class Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Class code already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete class
export const deleteClass = async (req, res) => {
  try {
    const classItem = await Class.findByIdAndDelete(req.params.id);

    if (!classItem) {
      return res.status(404).json({
        success: false,
        message: "Class not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Class deleted successfully",
    });
  } catch (error) {
    console.error("Delete Class Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update class status (active/inactive)
export const updateClassStatus = async (req, res) => {
  try {
    const { isActive } = req.body;

    if (isActive === undefined) {
      return res.status(400).json({
        success: false,
        message: "Please provide isActive status",
      });
    }

    const classItem = await Class.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true, runValidators: true }
    );

    if (!classItem) {
      return res.status(404).json({
        success: false,
        message: "Class not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Class status updated successfully",
      data: classItem,
    });
  } catch (error) {
    console.error("Update Class Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get classes by department
export const getClassesByDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params;

    const classes = await Class.find({
      department: departmentId,
      ...(req.user?.schoolId && { schoolId: req.user.schoolId }),
    })
      .sort({ name: 1 })
      .select("name code academicYear");

    res.status(200).json({
      success: true,
      count: classes.length,
      data: classes,
    });
  } catch (error) {
    console.error("Get Classes By Department Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get classes by academic year
export const getClassesByAcademicYear = async (req, res) => {
  try {
    const { academicYear } = req.params;

    const classes = await Class.find({
      academicYear,
      ...(req.user?.schoolId && { schoolId: req.user.schoolId }),
    })
      .sort({ name: 1 })
      .populate("department", "name code")
      .select("name code department");

    res.status(200).json({
      success: true,
      count: classes.length,
      data: classes,
    });
  } catch (error) {
    console.error("Get Classes By Academic Year Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
