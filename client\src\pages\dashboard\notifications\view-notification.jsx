import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useNotification } from "@/context/notification-context";
import { PageHeader } from "@/components/dashboard/page-header";
import {
  <PERSON>,
  <PERSON>Header,
  <PERSON><PERSON>ontent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/utils/date-filters";
import {
  Bell,
  AlertTriangle,
  ArrowLeft,
  <PERSON>,
  Send,
  Trash2,
  <PERSON>r,
  Clock<PERSON>,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import {
  getNotificationIcon,
  getPriorityBadgeVariant,
  getStatusIcon,
  getStatusBadgeVariant,
} from "@/utils/get-functions";

const ViewNotification = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchNotificationById, removeNotification, sendNotificationNow } =
    useNotification();
  const [notification, setNotification] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [openDelete, setOpenDelete] = useState(false);
  const [openSend, setOpenSend] = useState(false);

  useEffect(() => {
    const fetchNotification = async () => {
      try {
        setIsLoading(true);
        const data = await fetchNotificationById(id);
        setNotification(data);
      } catch (error) {
        console.error("Failed to fetch notification:", error);
        toast.error("Error", {
          description: errorMessage || "Failed to load notification details",
        });
        navigate("/dashboard/notifications");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchNotification();
    } else {
      console.error("No notification ID provided");
      setIsLoading(false);
    }
  }, [id, navigate]);

  const handleDelete = async () => {
    try {
      await removeNotification(notification._id);
      toast.success("Notification deleted successfully", {
        description: `${notification.title} has been removed.`,
      });
      navigate("/dashboard/notifications");
    } catch (error) {
      console.error("Failed to delete notification:", error);
      toast.error("Delete Failed", {
        description: error.message || "Failed to delete notification",
      });
    }
  };

  const handleSend = async () => {
    try {
      if (notification.status === "sent") {
        toast.error("Notification has already been sent");
        setOpenSend(false);
        return;
      }
      await sendNotificationNow(notification._id);
      toast.success("Notification sent successfully", {
        description: `${notification.title} has been sent to recipients.`,
      });
      const updatedNotification = await fetchNotificationById(id);
      setNotification(updatedNotification);
      setOpenSend(false);
    } catch (error) {
      console.error("Failed to send notification:", error);
      toast.error("Send Failed", {
        description: errorMessage || "Failed to send notification",
      });
    }
  };

  if (isLoading) {
    return <NotificationDetailSkeleton />;
  }

  if (!notification) {
    return (
      <div className="min-h-screen bg-background">
        <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
          <PageHeader
            title="Notification Not Found"
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Notifications", href: "/dashboard/notifications" },
              { label: "Not Found" },
            ]}
            actions={[
              {
                label: "Back to Notifications",
                icon: ArrowLeft,
                href: "/dashboard/notifications",
              },
            ]}
          />
          <Card className="py-0">
            <CardContent className="">
              <div className="text-center">
                <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                  <Bell className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="mt-6 text-xl font-semibold text-foreground">
                  Notification not found
                </h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  The notification you're looking for doesn't exist or has been
                  removed.
                </p>
                <Button
                  className="mt-6"
                  onClick={() => navigate("/dashboard/notifications")}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Notifications
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <Container className="py-8">
      <div className="space-y-6">
        <PageHeader
          title="Notification Details"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Notifications", href: "/dashboard/notifications" },
            { label: notification.title },
          ]}
          actions={[
            ...(notification.status !== "sent"
              ? [
                  {
                    label: "Edit",
                    icon: Edit,
                    href: `/dashboard/notifications/${notification._id}/edit`,
                  },
                ]
              : []),
            {
              label: "Back to Notifications",
              icon: ArrowLeft,
              href: "/dashboard/notifications",
            },
          ]}
        />

        {/* Main Notification Card */}
        <Card className="py-0">
          <CardHeader className="border-b bg-card px-6 py-6 rounded-t-md">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-muted border">
                    {getNotificationIcon(notification.type)}
                  </div>
                </div>
                <div className="min-w-0 flex-1">
                  <h1 className="text-2xl font-bold text-foreground mb-2">
                    {notification.title}
                  </h1>
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span className="font-medium capitalize">
                      {notification.recipients}
                    </span>
                    <span>•</span>
                    <span>{notification.type}</span>
                    <span>•</span>
                    <span>ID: {notification._id}</span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Badge variant={getPriorityBadgeVariant(notification.priority)}>
                  {notification.priority} Priority
                </Badge>
                <Badge variant={getStatusBadgeVariant(notification.status)}>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(notification.status)}
                    <span className="capitalize">{notification.status}</span>
                  </div>
                </Badge>
              </div>
            </div>
          </CardHeader>

          <CardContent className="px-6 py-6">
            {/* Message Section */}
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-foreground mb-3">
                Message Content
              </h2>
              <div className="rounded-lg bg-muted p-4 border">
                <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                  {notification.message}
                </p>
              </div>
            </div>

            <Separator className="my-6" />

            {/* Details Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Creator Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <User className="mr-2 h-5 w-5 text-muted-foreground" />
                  Creator Information
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Name
                    </label>
                    <p className="text-foreground font-medium">
                      {notification.createdByName ||
                        notification.createdBy?.name ||
                        "Unknown User"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Email
                    </label>
                    <p className="text-muted-foreground">
                      {notification.createdByEmail ||
                        notification.createdBy?.email ||
                        "No email provided"}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Role
                    </label>
                    <p className="text-muted-foreground capitalize">
                      {notification.createdByRole ||
                        notification.createdBy?.role ||
                        "Unknown Role"}
                    </p>
                  </div>
                </div>
              </div>

              {/* Timeline Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-foreground flex items-center">
                  <Clock3 className="mr-2 h-5 w-5 text-muted-foreground" />
                  Timeline
                </h3>
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Created
                    </label>
                    <p className="text-foreground font-medium">
                      {formatDate(notification.createdAt)}
                    </p>
                  </div>
                  {notification.scheduledFor && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Scheduled For
                      </label>
                      <p className="text-foreground font-medium">
                        {formatDate(notification.scheduledFor)}
                      </p>
                    </div>
                  )}
                  {notification.sentAt && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        Sent At
                      </label>
                      <p className="text-foreground font-medium">
                        {formatDate(notification.sentAt)}
                      </p>
                    </div>
                  )}
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      Last Updated
                    </label>
                    <p className="text-muted-foreground">
                      {formatDate(
                        notification.updatedAt || notification.createdAt
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>

          <CardFooter className="border-t bg-muted/50 px-6 py-4">
            <div className="flex w-full items-center justify-between">
              <Button
                variant="outline"
                onClick={() => navigate("/dashboard/notifications")}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Notifications
              </Button>

              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  className="text-destructive border-destructive/20 hover:bg-destructive/10"
                  onClick={() => setOpenDelete(true)}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </Button>

                {notification.status === "scheduled" && (
                  <Button
                    variant="outline"
                    className="text-primary border-primary/20 hover:bg-primary/10"
                    onClick={() => setOpenSend(true)}
                  >
                    <Send className="mr-2 h-4 w-4" />
                    Send Now
                  </Button>
                )}

                {notification.status !== "sent" && (
                  <Button
                    onClick={() =>
                      navigate(
                        `/dashboard/notifications/${notification._id}/edit`
                      )
                    }
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Notification
                  </Button>
                )}
              </div>
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
                <AlertTriangle className="h-5 w-5 text-destructive" />
              </div>
              <div>
                <AlertDialogTitle className="text-left">
                  Delete Notification
                </AlertDialogTitle>
              </div>
            </div>
            <AlertDialogDescription className="text-left">
              Are you sure you want to delete "{notification.title}"? This
              action cannot be undone and the notification will be permanently
              removed from the system.
              {notification.status === "sent" && (
                <span className="block mt-2 text-amber-600 font-medium">
                  Note: This notification has already been sent to recipients.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive hover:bg-destructive/90"
            >
              Delete Notification
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Send Confirmation Dialog */}
      <AlertDialog open={openSend} onOpenChange={setOpenSend}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <div className="flex items-center space-x-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                <Send className="h-5 w-5 text-primary" />
              </div>
              <div>
                <AlertDialogTitle className="text-left">
                  Send Notification Now
                </AlertDialogTitle>
              </div>
            </div>
            <AlertDialogDescription className="text-left">
              This will send "{notification.title}" immediately instead of
              waiting for the scheduled time. Are you sure you want to proceed?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleSend}>Send Now</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Container>
  );
};

// Enhanced Skeleton Loading Component
const NotificationDetailSkeleton = () => {
  return (
    <div className="min-h-screen bg-background">
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Header Skeleton */}
        <div className="mb-8 space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="flex space-x-2">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-20" />
            </div>
          </div>
        </div>

        {/* Main Card Skeleton */}
        <Card>
          <CardHeader className="border-b bg-card px-6 py-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="space-y-3">
                  <Skeleton className="h-8 w-80" />
                  <Skeleton className="h-4 w-64" />
                </div>
              </div>
              <div className="flex space-x-3">
                <Skeleton className="h-6 w-24 rounded-full" />
                <Skeleton className="h-6 w-20 rounded-full" />
              </div>
            </div>
          </CardHeader>

          <CardContent className="px-6 py-6">
            {/* Message Section Skeleton */}
            <div className="mb-8 space-y-3">
              <Skeleton className="h-6 w-32" />
              <div className="rounded-lg bg-muted p-4 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            </div>

            <Separator className="my-6" />

            {/* Details Grid Skeleton */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="space-y-4">
                <Skeleton className="h-6 w-40" />
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-32" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-48" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <Skeleton className="h-6 w-24" />
                <div className="rounded-lg border bg-card p-4 space-y-3">
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-40" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-5 w-40" />
                  </div>
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-5 w-32" />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>

          <CardFooter className="border-t bg-muted/50 px-6 py-4">
            <div className="flex w-full items-center justify-between">
              <Skeleton className="h-10 w-40" />
              <div className="flex space-x-3">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-28" />
                <Skeleton className="h-10 w-36" />
              </div>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ViewNotification;
