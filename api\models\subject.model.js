import mongoose from "mongoose";

const SubjectSchema = new mongoose.Schema(
  {
    // Basic Information
    name: {
      type: String,
      required: [true, "Subject name is required"],
      trim: true,
    },
    code: {
      type: String,
      required: [true, "Subject code is required"],
      trim: true,
      minlength: [2, "Code must be at least 2 characters"],
      maxlength: [10, "Code must not exceed 10 characters"],
    },
    type: {
      type: String,
      required: [true, "Subject type is required"],
      trim: true,
    },
    department: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Department",
      required: [true, "Department is required"],
    },
    description: {
      type: String,
      trim: true,
      minlength: [10, "Description must be at least 10 characters"],
      maxlength: [500, "Description must not exceed 500 characters"],
    },

    // Academic Details
    credit: {
      type: Number,
      required: [true, "Credit hours is required"],
      min: [1, "Credit hours must be at least 1"],
      max: [10, "Credit hours cannot exceed 10"],
    },
    semester: {
      type: Number,
      required: [true, "Semester is required"],
      min: [1, "Semester must be at least 1"],
      max: [8, "<PERSON>mester cannot exceed 8"],
    },
    academicYear: {
      type: String,
      required: [true, "Academic year is required"],
      trim: true,
    },
    totalMarks: {
      type: Number,
      required: [true, "Total marks is required"],
      min: [1, "Total marks must be at least 1"],
      max: [1000, "Total marks cannot exceed 1000"],
    },
    passMarks: {
      type: Number,
      required: [true, "Pass marks is required"],
      min: [1, "Pass marks must be at least 1"],
    },
    prerequisites: {
      type: String,
      trim: true,
    },

    // Other details
    hasLabs: {
      type: Boolean,
      default: false,
    },
    hasPracticalExams: {
      type: Boolean,
      default: false,
    },
    hasTheoryExams: {
      type: Boolean,
      default: true,
    },
    hasAssignments: {
      type: Boolean,
      default: false,
    },
    isElective: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },

    // Additional Information
    notes: {
      type: String,
      trim: true,
    },

    // Relationships
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },

    // Status
    status: {
      type: String,
      enum: ["active", "inactive"],
      default: "active",
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
SubjectSchema.index({ schoolId: 1 });
SubjectSchema.index({ code: 1, schoolId: 1 }, { unique: true });
SubjectSchema.index({ name: 1, schoolId: 1 });
SubjectSchema.index({ department: 1 });
SubjectSchema.index({ semester: 1 });
SubjectSchema.index({ academicYear: 1 });

export default mongoose.model("Subject", SubjectSchema);
