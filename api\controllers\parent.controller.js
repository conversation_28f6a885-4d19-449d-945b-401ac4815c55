import Parent from "../models/parent.model.js";
import User from "../models/user.model.js";
import bcrypt from "bcryptjs";

// Helper function to hash password
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(12);
  return await bcrypt.hash(password, salt);
};

// Helper function to compare password
export const comparePassword = async (candidatePassword, hashedPassword) => {
  return await bcrypt.compare(candidatePassword, hashedPassword);
};

// Create a new parent
export const createParent = async (req, res) => {
  try {
    const parentData = req.body;

    if (!parentData.schoolId && req.user?.schoolId) {
      parentData.schoolId = req.user.schoolId;
    }

    const existingParent = await Parent.findOne({ email: parentData.email });
    if (existingParent) {
      return res.status(400).json({
        success: false,
        message: "A parent with this email already exists",
      });
    }

    const existingUser = await User.findOne({ email: parentData.email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "A user with this email already exists",
      });
    }

    // Hash password if provided
    if (parentData.password) {
      parentData.password = await hashPassword(parentData.password);
    }

    // Create the parent record
    const newParent = new Parent(parentData);
    await newParent.save();

    // Create corresponding user account if isActive is true
    if (parentData.isActive && parentData.email && parentData.password) {
      const userData = {
        name: `${parentData.firstName} ${parentData.lastName}`,
        email: parentData.email,
        password: await hashPassword(parentData.password),
        role: "parent",
        schoolId: req.user?.schoolId || null,
      };

      const newUser = new User(userData);
      await newUser.save();
    }

    const parentResponse = newParent.toObject();
    delete parentResponse.password;

    res.status(201).json({
      success: true,
      message: "Parent created successfully",
      data: parentResponse,
    });
  } catch (error) {
    console.error("Create Parent Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Email already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all parents
export const getAllParents = async (req, res) => {
  try {
    let query = {};

    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    if (req.query.schoolId && req.user?.role === "admin") {
      query.schoolId = req.query.schoolId;
    }

    const parents = await Parent.find(query)
      .select("-password")
      .populate("schoolId", "name")
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: parents.length,
      data: parents,
    });
  } catch (error) {
    console.error("Get All Parents Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get parent by ID
export const getParentById = async (req, res) => {
  try {
    const parent = await Parent.findById(req.params.id)
      .select("-password")
      .populate("schoolId", "name");

    if (!parent) {
      return res.status(404).json({
        success: false,
        message: "Parent not found",
      });
    }

    res.status(200).json({
      success: true,
      data: parent,
    });
  } catch (error) {
    console.error("Get Parent By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update parent
export const updateParent = async (req, res) => {
  try {
    const parentData = req.body;
    const parentId = req.params.id;

    // Add schoolId from authenticated user if not provided
    if (!parentData.schoolId && req.user?.schoolId) {
      parentData.schoolId = req.user.schoolId;
    }

    const existingParent = await Parent.findById(parentId);
    if (!existingParent) {
      return res.status(404).json({
        success: false,
        message: "Parent not found",
      });
    }

    if (parentData.email && parentData.email !== existingParent.email) {
      const emailExists = await Parent.findOne({
        email: parentData.email,
        _id: { $ne: parentId },
      });
      if (emailExists) {
        return res.status(400).json({
          success: false,
          message: "A parent with this email already exists",
        });
      }

      const userEmailExists = await User.findOne({
        email: parentData.email,
      });
      if (userEmailExists) {
        return res.status(400).json({
          success: false,
          message: "A user with this email already exists",
        });
      }
    }

    if (parentData.password === "") {
      delete parentData.password;
    } else if (parentData.password) {
      parentData.password = await hashPassword(parentData.password);
    }

    const parent = await Parent.findByIdAndUpdate(parentId, parentData, {
      new: true,
      runValidators: true,
    })
      .select("-password")
      .populate("schoolId", "name");

    if (!parent) {
      return res.status(404).json({
        success: false,
        message: "Parent not found",
      });
    }

    const existingUser = await User.findOne({ email: existingParent.email });
    if (existingUser) {
      const userUpdateData = {
        name: `${parentData.firstName || existingParent.firstName} ${
          parentData.lastName || existingParent.lastName
        }`,
        email: parentData.email || existingParent.email,
      };

      // Only update password if provided
      if (parentData.password) {
        userUpdateData.password = await hashPassword(parentData.password);
      }

      await User.findByIdAndUpdate(existingUser._id, userUpdateData);
    } else if (parentData.isActive && parentData.email && parentData.password) {
      // Create user account if it doesn't exist and parent is active
      const userData = {
        name: `${parentData.firstName || existingParent.firstName} ${
          parentData.lastName || existingParent.lastName
        }`,
        email: parentData.email || existingParent.email,
        password: await hashPassword(parentData.password),
        role: "parent",
        schoolId: req.user?.schoolId || null,
      };

      const newUser = new User(userData);
      await newUser.save();
    }

    res.status(200).json({
      success: true,
      message: "Parent updated successfully",
      data: parent,
    });
  } catch (error) {
    console.error("Update Parent Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Email already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete parent
export const deleteParent = async (req, res) => {
  try {
    const parent = await Parent.findById(req.params.id);

    if (!parent) {
      return res.status(404).json({
        success: false,
        message: "Parent not found",
      });
    }

    await User.findOneAndDelete({ email: parent.email });
    await Parent.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: "Parent deleted successfully",
    });
  } catch (error) {
    console.error("Delete Parent Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update parent status
export const updateParentStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (!status || !["active", "inactive"].includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Please provide a valid status",
      });
    }

    const parent = await Parent.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    )
      .select("-password")
      .populate("schoolId", "name");

    if (!parent) {
      return res.status(404).json({
        success: false,
        message: "Parent not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Parent status updated successfully",
      data: parent,
    });
  } catch (error) {
    console.error("Update Parent Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
