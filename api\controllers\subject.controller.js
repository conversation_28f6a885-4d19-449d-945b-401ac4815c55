import Subject from "../models/subject.model.js";

// Create a new subject
export const createSubject = async (req, res) => {
  try {
    const subjectData = req.body;

    if (!subjectData.schoolId && req.user?.schoolId) {
      subjectData.schoolId = req.user.schoolId;
    }

    const newSubject = new Subject(subjectData);
    await newSubject.save();

    res.status(201).json({
      success: true,
      message: "Subject created successfully",
      data: newSubject,
    });
  } catch (error) {
    console.error("Create Subject Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Subject code already exists in this school",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all subjects
export const getAllSubjects = async (req, res) => {
  try {
    const { schoolId, departmentId, semester, academicYear, isActive } =
      req.query;

    let query = {};

    // Apply filters
    if (schoolId) {
      query.schoolId = schoolId;
    } else if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    if (departmentId) {
      query.department = departmentId;
    }

    if (semester) {
      query.semester = semester;
    }

    if (academicYear) {
      query.academicYear = academicYear;
    }

    if (isActive !== undefined) {
      query.isActive = isActive === "true";
    }

    const subjects = await Subject.find(query)
      .sort({ createdAt: -1 })
      .populate("department", "name code")
      .populate("schoolId", "name");

    res.status(200).json({
      success: true,
      count: subjects.length,
      data: subjects,
    });
  } catch (error) {
    console.error("Get All Subjects Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get subject by ID
export const getSubjectById = async (req, res) => {
  try {
    const subject = await Subject.findById(req.params.id)
      .populate("department", "name code")
      .populate("schoolId", "name");

    if (!subject) {
      return res.status(404).json({
        success: false,
        message: "Subject not found",
      });
    }

    res.status(200).json({
      success: true,
      data: subject,
    });
  } catch (error) {
    console.error("Get Subject By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update subject
export const updateSubject = async (req, res) => {
  try {
    const subjectData = req.body;

    const subject = await Subject.findByIdAndUpdate(
      req.params.id,
      subjectData,
      {
        new: true,
        runValidators: true,
      }
    ).populate("department", "name code");

    if (!subject) {
      return res.status(404).json({
        success: false,
        message: "Subject not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Subject updated successfully",
      data: subject,
    });
  } catch (error) {
    console.error("Update Subject Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Subject code already exists in this school",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete subject
export const deleteSubject = async (req, res) => {
  try {
    const subject = await Subject.findByIdAndDelete(req.params.id);

    if (!subject) {
      return res.status(404).json({
        success: false,
        message: "Subject not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Subject deleted successfully",
    });
  } catch (error) {
    console.error("Delete Subject Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update subject status (active/inactive)
export const updateSubjectStatus = async (req, res) => {
  try {
    const { isActive } = req.body;

    if (isActive === undefined) {
      return res.status(400).json({
        success: false,
        message: "Please provide isActive status",
      });
    }

    const subject = await Subject.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true, runValidators: true }
    );

    if (!subject) {
      return res.status(404).json({
        success: false,
        message: "Subject not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Subject status updated successfully",
      data: subject,
    });
  } catch (error) {
    console.error("Update Subject Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get subjects by department
export const getSubjectsByDepartment = async (req, res) => {
  try {
    const { departmentId } = req.params;

    const subjects = await Subject.find({
      department: departmentId,
      ...(req.user?.schoolId && { schoolId: req.user.schoolId }),
    })
      .sort({ name: 1 })
      .select("name code credit semester");

    res.status(200).json({
      success: true,
      count: subjects.length,
      data: subjects,
    });
  } catch (error) {
    console.error("Get Subjects By Department Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
