import React, { useEffect, useState } from "react";
import { Users, BookOpen, Calendar, GraduationCap } from "lucide-react";
import { WelcomeBanner } from "@/components/dashboard/welcome-banner";
import { StatCard } from "@/components/dashboard/stat-card";
import { useAuth } from "@/context/auth-context";
import { useDashboard } from "@/context/dashboard-context";
import { ChartAreaInteractive } from "@/components/dashboard/chart-area-interactive";
import { getSchoolAdminChartData } from "@/api/dashboard-api";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";
import { StudentColumns } from "@/pages/dashboard/school-admin/students/student-columns";
import { TeacherColumns } from "@/pages/dashboard/school-admin/teachers/teacher-columns";
import { ParentColumns } from "@/pages/dashboard/school-admin/parents/parent-columns";
import { useStudent } from "@/context/student-context";
import { useTeacher } from "@/context/teacher-context";
import { useParent } from "@/context/parent-context";

const SchoolAdminOverview = () => {
  const { user, isLoading: authLoading } = useAuth();
  const {
    dashboardData,
    isLoading: dashboardLoading,
    fetchSchoolAdminDashboardStats,
  } = useDashboard();
  const {
    students,
    isLoading: studentsLoading,
    fetchAllStudents,
  } = useStudent();
  const {
    teachers,
    isLoading: teachersLoading,
    fetchAllTeachers,
  } = useTeacher();
  const { parents, isLoading: parentsLoading, fetchAllParents } = useParent();

  const [chartData, setChartData] = useState([]);
  const [chartLoading, setChartLoading] = useState(true);

  useEffect(() => {
    if (user && user.role === "school-admin") {
      fetchSchoolAdminDashboardStats();
      fetchAllStudents();
      fetchAllTeachers();
      fetchAllParents();
      fetchChartData();
    }
  }, [user]);

  const fetchChartData = async (timeRange = "30d") => {
    setChartLoading(true);
    try {
      const response = await getSchoolAdminChartData(timeRange);
      if (response.success) {
        setChartData(response.data);
      } else {
        setChartData([]);
      }
    } catch (error) {
      setChartData([]);
    } finally {
      setChartLoading(false);
    }
  };

  const isLoading = authLoading || dashboardLoading;
  const stats = dashboardData?.overview;

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-8">
      <WelcomeBanner
        user={user}
        isLoading={isLoading}
        title="School Admin Dashboard"
        subtitle="Manage your school's students, teachers, and academic programs"
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Students"
          value={stats?.totalStudents?.current || 0}
          description={`+${
            stats?.totalStudents?.lastMonth || 0
          } from last month`}
          icon={Users}
          isLoading={isLoading}
          trend={stats?.totalStudents?.trend || "neutral"}
        />
        <StatCard
          title="Total Teachers"
          value={stats?.totalTeachers?.current || 0}
          description={`+${
            stats?.totalTeachers?.lastMonth || 0
          } from last month`}
          icon={GraduationCap}
          isLoading={isLoading}
          trend={stats?.totalTeachers?.trend || "neutral"}
        />
        <StatCard
          title="Total Notifications"
          value={stats?.totalNotifications?.current || 0}
          description={`+${
            stats?.totalNotifications?.lastMonth || 0
          } from last month`}
          icon={BookOpen}
          isLoading={isLoading}
          trend={stats?.totalNotifications?.trend || "neutral"}
        />
        <StatCard
          title="Upcoming Events"
          value={8}
          description="This week"
          icon={Calendar}
          isLoading={isLoading}
        />
      </div>

      <ChartAreaInteractive
        title="Students & Teachers Creation"
        description={
          chartData.length > 0
            ? `${chartData.reduce(
                (sum, item) => sum + (item.students || 0),
                0
              )} students and ${chartData.reduce(
                (sum, item) => sum + (item.teachers || 0),
                0
              )} teachers created`
            : "Students and teachers creation over time"
        }
        data={chartData}
        dataKeys={["students", "teachers"]}
        isLoading={chartLoading}
        onTimeRangeChange={fetchChartData}
        height="250px"
      />

      <Tabs defaultValue="students" className="w-full mt-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="students">Students</TabsTrigger>
          <TabsTrigger value="teachers">Teachers</TabsTrigger>
          <TabsTrigger value="parents">Parents</TabsTrigger>
        </TabsList>
        <div className="mt-2">
          <TabsContent value="students">
            {studentsLoading ? (
              <DataTableSkeleton />
            ) : (
              <DataTable
                data={students}
                columns={StudentColumns()}
                model="student"
              />
            )}
          </TabsContent>
          <TabsContent value="teachers">
            {teachersLoading ? (
              <DataTableSkeleton />
            ) : (
              <DataTable
                data={teachers}
                columns={TeacherColumns()}
                model="teacher"
              />
            )}
          </TabsContent>
          <TabsContent value="parents">
            {parentsLoading ? (
              <DataTableSkeleton />
            ) : (
              <DataTable
                data={parents}
                columns={ParentColumns()}
                model="parent"
              />
            )}
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
};

export default SchoolAdminOverview;
