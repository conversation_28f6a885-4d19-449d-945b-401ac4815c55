import { useEffect, useState } from "react";
import { useNotification } from "@/context/notification-context";
import { <PERSON>, AlertTriangle, PlusCircle, Calendar, Clock } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { NotificationCard } from "@/pages/dashboard/notifications/notification-card";
import { DataCardSearchbar } from "@/components/data-card/data-card-component/data-card-searchbar";
import { DataCardsSkeleton } from "@/components/data-card/data-card-skeleton/data-cards-skeleton";

const NotificationDirectory = () => {
  const {
    notifications,
    isLoading,
    fetchAllNotifications,
    removeNotification,
    sendNotificationNow,
  } = useNotification();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");

  useEffect(() => {
    fetchAllNotifications();
  }, []);

  const filteredNotifications = notifications.filter((notification) => {
    const matchesSearch =
      notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filterStatus === "all" || notification.status === filterStatus;
    const matchesPriority =
      filterPriority === "all" || notification.priority === filterPriority;

    return matchesSearch && matchesStatus && matchesPriority;
  });

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Notification Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Notifications" },
          ]}
          actions={[
            {
              label: "New Notification",
              icon: PlusCircle,
              href: "/dashboard/notifications/create",
            },
          ]}
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Notifications"
            value={notifications.length}
            description="All notifications"
            icon={Bell}
            isLoading={isLoading}
            trend="positive"
          />
          <StatCard
            title="High Priority"
            value={
              notifications.filter(
                (notification) => notification.priority === "High"
              ).length
            }
            description="Urgent notifications"
            icon={AlertTriangle}
            isLoading={isLoading}
            trend="negative"
          />
          <StatCard
            title="Scheduled"
            value={
              notifications.filter(
                (notification) => notification.status === "scheduled"
              ).length
            }
            description="Pending delivery"
            icon={Calendar}
            isLoading={isLoading}
          />
          <StatCard
            title="Recent Notifications"
            value={
              notifications.filter((notification) => {
                const createdAt = new Date(notification.createdAt);
                const sevenDaysAgo = new Date();
                sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
                return createdAt >= sevenDaysAgo;
              }).length
            }
            description="Sent in last 7 days"
            icon={Clock}
            isLoading={isLoading}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Filters Sidebar */}
          <div className="">
            <Card>
              <CardHeader className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                <h3 className="text-2xl font-bold">Filters</h3>
              </CardHeader>
              <CardContent>
                <DataCardSearchbar
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  placeholder="Search notifications..."
                  filterOptions={[
                    {
                      value: filterStatus,
                      onChange: setFilterStatus,
                      placeholder: "Status",
                      options: [
                        { value: "all", label: "All Status" },
                        { value: "draft", label: "Draft" },
                        { value: "scheduled", label: "Scheduled" },
                        { value: "sent", label: "Sent" },
                        { value: "failed", label: "Failed" },
                      ],
                    },
                    {
                      value: filterPriority,
                      onChange: setFilterPriority,
                      placeholder: "Priority",
                      options: [
                        { value: "all", label: "All Priority" },
                        { value: "High", label: "High" },
                        { value: "Medium", label: "Medium" },
                        { value: "Low", label: "Low" },
                      ],
                    },
                  ]}
                />
              </CardContent>
            </Card>
          </div>
          {/* Notifications Area */}
          <div className="col-span-2">
            <div className="space-y-4">
              {isLoading ? (
                <DataCardsSkeleton />
              ) : (
                <NotificationCards
                  notifications={filteredNotifications}
                  onEdit={(id) =>
                    navigate(`/dashboard/notifications/${id}/edit`)
                  }
                  onView={(id) => navigate(`/dashboard/notifications/${id}`)}
                  onDelete={removeNotification}
                  onSend={sendNotificationNow}
                />
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

// Notification Cards Component
const NotificationCards = ({
  notifications,
  onEdit,
  onView,
  onDelete,
  onSend,
}) => {
  if (notifications.length === 0) {
    return (
      <div className="text-center py-12">
        <Bell className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No notifications found</h3>
        <p className="mt-2 text-muted-foreground">
          Get started by creating your first notification.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {notifications.map((notification) => (
        <NotificationCard
          key={notification._id}
          notification={notification}
          onEdit={onEdit}
          onView={onView}
          onDelete={onDelete}
          onSend={onSend}
        />
      ))}
    </div>
  );
};

export default NotificationDirectory;
