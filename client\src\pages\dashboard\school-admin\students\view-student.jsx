import { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useStudent } from "@/context/student-context";
import { PageHeader } from "@/components/dashboard/page-header";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/utils/date-filters";
import {
  User,
  ArrowLeft,
  Edit,
  Trash2,
  MapPin,
  FileText,
  Settings,
  Phone,
  Mail,
  GraduationCap,
  Heart,
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  Users,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { InfoCard } from "@/components/dashboard/info-card";
import { DataField } from "@/components/dashboard/data-field";
import { ProfileBanner } from "@/components/dashboard/profile-banner";
import { ViewDetailSkeleton } from "@/components/dashboard/view-detail-skeleton";

const ViewStudent = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchStudentById, removeStudent, updateStatus } = useStudent();
  const [student, setStudent] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [openDelete, setOpenDelete] = useState(false);
  const [openStatusChange, setOpenStatusChange] = useState(false);

  useEffect(() => {
    const fetchStudent = async () => {
      try {
        setIsLoading(true);
        const data = await fetchStudentById(id);
        setStudent(data);
      } catch (error) {
        console.error("Failed to fetch student:", error);
        toast.error("Error", {
          description: error.message || "Failed to load student details",
        });
        navigate("/dashboard/students");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchStudent();
    } else {
      setIsLoading(false);
    }
  }, []);

  const handleDelete = async () => {
    try {
      await removeStudent(student._id);
      toast.success("Student deleted successfully", {
        description: `${student.firstName} ${student.lastName} has been removed.`,
      });
      navigate("/dashboard/students");
    } catch (error) {
      console.error("Failed to delete student:", error);
      toast.error("Delete Failed", {
        description: error.message || "Failed to delete student",
      });
    }
  };

  const handleStatusChange = async () => {
    try {
      const newStatus = student.status === "active" ? "inactive" : "active";
      await updateStatus(student._id, { status: newStatus });
      setOpenStatusChange(false);
      setStudent((prevStudent) => ({
        ...prevStudent,
        status: newStatus,
      }));
      toast.success("Status updated successfully", {
        description: `${student.firstName} ${student.lastName}'s status has been changed to ${newStatus}.`, // Fixed unclosed string
      });
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Update Failed", {
        description: error.message || "Failed to update student status",
      });
    }
  };

  if (isLoading) {
    return <ViewDetailSkeleton />;
  }

  return (
    <Container className="py-8">
      <div className="space-y-6">
        <PageHeader
          title="Student Profile"
          isLoading={isLoading}
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Students", href: "/dashboard/students" },
            { label: `${student.firstName} ${student.lastName}` },
          ]}
          actions={[
            {
              label: "Edit Profile",
              icon: Edit,
              href: `/dashboard/students/${student._id}/edit`,
            },
            {
              label: "Back to Students",
              icon: ArrowLeft,
              href: "/dashboard/students",
              variant: "outline",
            },
          ]}
        />

        <ProfileBanner
          data={student}
          loading={isLoading}
          setOpenStatusChange={setOpenStatusChange}
          setOpenDelete={setOpenDelete}
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-1">
            <div className="grid grid-cols-1 gap-6">
              <InfoCard
                icon={User}
                title={`${student.firstName} ${student.lastName}`}
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="System Email"
                    value={student.email}
                    icon={Mail}
                    copyable
                  />
                  <DataField
                    label="Personal Email"
                    value={student.personalEmail}
                    icon={Mail}
                    copyable
                  />
                  <DataField
                    label="Phone"
                    value={student.phone}
                    icon={Phone}
                    copyable
                  />
                  {student.alternatePhone && (
                    <DataField
                      label="Alternate Phone"
                      value={student.alternatePhone}
                      icon={Phone}
                      copyable
                    />
                  )}
                  <DataField
                    label="Preferred Contact"
                    value={student.preferredContactMethod}
                  />
                </div>
              </InfoCard>

              <InfoCard
                icon={Shield}
                title="Identity Documents"
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="Aadhar Number"
                    value={student.aadharNumber}
                    copyable
                  />
                  <DataField
                    label="Student ID"
                    value={student.studentId}
                    copyable
                  />
                  <DataField
                    label="Roll Number"
                    value={student.rollNumber}
                    copyable
                  />
                  {student.profilePhoto && (
                    <DataField
                      label="Profile Photo"
                      value={student.profilePhoto}
                    />
                  )}
                </div>
              </InfoCard>

              <InfoCard
                icon={Shield}
                title="System Access"
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="System Email"
                    value={student.email}
                    icon={Mail}
                    copyable
                  />
                  <DataField
                    label="Account Status"
                    value={student.isActive ? "Active" : "Inactive"}
                  />
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                      Status
                    </label>
                    <Badge
                      variant={
                        student.status === "active" ? "default" : "secondary"
                      }
                      className={`w-fit ${
                        student.status === "active"
                          ? "bg-primary text-primary-foreground"
                          : "bg-secondary text-secondary-foreground"
                      }`}
                    >
                      {student.status === "active" ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1" />
                      )}
                      {student.status?.charAt(0).toUpperCase() +
                        student.status?.slice(1)}
                    </Badge>
                  </div>
                </div>
              </InfoCard>

              <InfoCard
                icon={FileText}
                title="Additional Notes"
                isLoading={isLoading}
              >
                <div className="bg-muted/50 rounded-lg p-4 min-h-[120px]">
                  <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                    {student.notes || "No additional notes available."}
                  </p>
                </div>
              </InfoCard>
            </div>
          </div>
          <div className="md:col-span-2 rounded-xl">
            <Tabs defaultValue="personal" className="w-full">
              <div className="mb-4">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="personal">
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline">Personal</span>
                  </TabsTrigger>
                  <TabsTrigger value="academic">
                    <GraduationCap className="h-4 w-4" />
                    <span className="hidden sm:inline">Academic</span>
                  </TabsTrigger>
                  <TabsTrigger value="contact">
                    <Phone className="h-4 w-4" />
                    <span className="hidden sm:inline">Contact</span>
                  </TabsTrigger>
                  <TabsTrigger value="system">
                    <Settings className="h-4 w-4" />
                    <span className="hidden sm:inline">System</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Personal Information Tab */}
              <TabsContent value="personal" className="space-y-6">
                <InfoCard
                  icon={User}
                  title="Personal Details"
                  isLoading={isLoading}
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <DataField label="First Name" value={student.firstName} />
                    <DataField label="Last Name" value={student.lastName} />
                    <DataField label="Gender" value={student.gender} />
                    <DataField
                      label="Date of Birth"
                      value={
                        student.dateOfBirth
                          ? formatDate(student.dateOfBirth)
                          : null
                      }
                    />
                    <DataField
                      label="Blood Group"
                      value={student.bloodGroup}
                      icon={Heart}
                    />
                    <DataField
                      label="Nationality"
                      value={student.nationality}
                    />
                    <DataField label="Religion" value={student.religion} />
                  </div>
                </InfoCard>

                {/* Parent Details InfoCard */}
                <InfoCard
                  icon={Users}
                  title="Parent Details"
                  isLoading={isLoading}
                >
                  <div className="space-y-4">
                    <DataField
                      label="Parent Name"
                      value={student.parent?.name}
                    />
                    <DataField
                      label="Parent ID"
                      value={student.parent?.id}
                      copyable
                    />
                  </div>
                </InfoCard>
              </TabsContent>

              {/* Academic Information Tab */}
              <TabsContent value="academic" className="space-y-6">
                <InfoCard
                  icon={GraduationCap}
                  title="Academic Details"
                  isLoading={isLoading}
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <DataField
                      label="Class"
                      value={student.class?.name || student.class}
                    />
                    <DataField
                      label="Section"
                      value={student.section?.name || student.section}
                    />
                    <DataField
                      label="Academic Year"
                      value={student.academicYear}
                    />
                    <DataField
                      label="Admission Date"
                      value={
                        student.admissionDate
                          ? formatDate(student.admissionDate)
                          : null
                      }
                    />
                    <DataField
                      label="Admission Type"
                      value={student.admissionType}
                    />
                    <DataField
                      label="Previous School"
                      value={student.previousSchool}
                    />
                  </div>
                </InfoCard>
              </TabsContent>

              {/* Contact Information Tab */}
              <TabsContent value="contact" className="space-y-6">
                <InfoCard
                  icon={Phone}
                  title="Contact Information"
                  isLoading={isLoading}
                >
                  <div className="space-y-4">
                    <DataField
                      label="Personal Email"
                      value={student.personalEmail}
                      icon={Mail}
                      copyable
                    />
                    <DataField
                      label="Phone Number"
                      value={student.phone}
                      icon={Phone}
                      copyable
                    />
                    {student.alternatePhone && (
                      <DataField
                        label="Alternate Phone"
                        value={student.alternatePhone}
                        icon={Phone}
                        copyable
                      />
                    )}
                    <DataField
                      label="Preferred Contact Method"
                      value={student.preferredContactMethod}
                    />
                  </div>
                </InfoCard>
                <InfoCard
                  icon={MapPin}
                  title="Address Information"
                  isLoading={isLoading}
                >
                  <div className="space-y-4">
                    <DataField label="Address" value={student.address} />
                    <div className="grid grid-cols-2 gap-4">
                      <DataField label="City" value={student.city} />
                      <DataField label="State" value={student.state} />
                      <DataField
                        label="Pincode"
                        value={student.pincode}
                        copyable
                      />
                      <DataField label="Country" value={student.country} />
                    </div>
                  </div>
                </InfoCard>
              </TabsContent>

              {/* System Information Tab */}
              <TabsContent value="system" className="space-y-6">
                <InfoCard icon={Clock} title="Timestamps" isLoading={isLoading}>
                  <div className="space-y-4">
                    <DataField
                      label="Created At"
                      value={formatDate(student.createdAt)}
                    />
                    <DataField
                      label="Last Updated"
                      value={formatDate(student.updatedAt)}
                    />
                  </div>
                </InfoCard>
                <InfoCard
                  icon={Settings}
                  title="System Details"
                  isLoading={isLoading}
                >
                  <div className="space-y-4">
                    <DataField
                      label="Student ID"
                      value={student._id}
                      copyable
                    />
                    <DataField
                      label="System Email"
                      value={student.email}
                      copyable
                    />
                    <div className="space-y-1">
                      <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                        Status
                      </label>
                      <Badge
                        variant={
                          student.status === "active" ? "default" : "secondary"
                        }
                        className={`w-fit ${
                          student.status === "active"
                            ? "bg-primary text-primary-foreground"
                            : "bg-secondary text-secondary-foreground"
                        }`}
                      >
                        {student.status === "active" ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <XCircle className="h-3 w-3 mr-1" />
                        )}
                        {student.status?.charAt(0).toUpperCase() +
                          student.status?.slice(1)}
                      </Badge>
                    </div>
                  </div>
                </InfoCard>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-destructive" />
              Delete {student.firstName} {student.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              student's profile and remove all associated data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive hover:bg-destructive/90"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Student
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Status Change Confirmation Dialog */}
      <AlertDialog open={openStatusChange} onOpenChange={setOpenStatusChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              {student.status === "active" ? (
                <XCircle className="h-5 w-5 text-muted-foreground" />
              ) : (
                <CheckCircle className="h-5 w-5 text-primary" />
              )}
              {student.status === "active" ? "Deactivate" : "Activate"}{" "}
              {student.firstName} {student.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to{" "}
              {student.status === "active" ? "deactivate" : "activate"} this
              student's account?
              {student.status === "active"
                ? " They will lose access to the system until reactivated."
                : " They will regain full access to the system."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleStatusChange}>
              {student.status === "active" ? "Deactivate" : "Activate"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Container>
  );
};

export default ViewStudent;
