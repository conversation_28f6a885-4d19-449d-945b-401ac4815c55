import { createContext, useContext, useState } from "react";
import {
  createClass,
  getClasses,
  getClassById,
  updateClass,
  deleteClass,
  updateClassStatus,
  getClassesByDepartment,
  getClassesByAcademicYear,
} from "@/api/class-api";

const ClassContext = createContext({
  classes: [],
  currentClass: null,
  isLoading: false,
  error: null,
  addClass: () => {},
  fetchAllClasses: () => {},
  fetchClassById: () => {},
  editClass: () => {},
  removeClass: () => {},
  updateStatus: () => {},
  fetchClassesByDepartment: () => {},
  fetchClassesByAcademicYear: () => {},
});

export const ClassProvider = ({ children }) => {
  const [classes, setClasses] = useState([]);
  const [currentClass, setCurrentClass] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addClass = async (classData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createClass(classData);
      setClasses((prevClasses) => [...prevClasses, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create class");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllClasses = async (filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getClasses(filters);
      setClasses(response.data);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to fetch classes");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchClassById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getClassById(id);
      setCurrentClass(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch class with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editClass = async (id, classData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateClass(id, classData);
      setClasses(classes.map((cls) => (cls._id === id ? response.data : cls)));
      if (currentClass && currentClass._id === id) {
        setCurrentClass(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update class with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeClass = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteClass(id);
      setClasses(classes.filter((cls) => cls._id !== id));
      if (currentClass && currentClass._id === id) {
        setCurrentClass(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete class with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, isActive) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateClassStatus(id, isActive);
      setClasses(classes.map((cls) => (cls._id === id ? response.data : cls)));
      if (currentClass && currentClass._id === id) {
        setCurrentClass(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update class status with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const fetchClassesByDepartment = async (departmentId) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getClassesByDepartment(departmentId);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(
        error.message ||
          `Failed to fetch classes for department: ${departmentId}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const fetchClassesByAcademicYear = async (academicYear) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getClassesByAcademicYear(academicYear);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(
        error.message ||
          `Failed to fetch classes for academic year: ${academicYear}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  // Create class options for select inputs
  const classOptions = classes.map((cls) => ({
    label: `${cls.name} (${cls.code})`,
    value: cls._id,
    id: cls._id,
    name: cls.name,
    code: cls.code,
    academicYear: cls.academicYear,
    department: cls.department,
  }));

  return (
    <ClassContext.Provider
      value={{
        classes,
        setClasses,
        currentClass,
        isLoading,
        error,
        addClass,
        fetchAllClasses,
        fetchClassById,
        editClass,
        removeClass,
        updateStatus,
        fetchClassesByDepartment,
        fetchClassesByAcademicYear,
        classOptions,
      }}
    >
      {children}
    </ClassContext.Provider>
  );
};

export const useClass = () => useContext(ClassContext);
