import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Clock } from "lucide-react";

const WEEKDAYS = [
  { key: "monday", label: "Monday", short: "Mon" },
  { key: "tuesday", label: "Tuesday", short: "Tue" },
  { key: "wednesday", label: "Wednesday", short: "Wed" },
  { key: "thursday", label: "Thursday", short: "Thu" },
  { key: "friday", label: "Friday", short: "Fri" },
  { key: "saturday", label: "Saturday", short: "Sat" },
];

export function TimetableSkeleton() {
  const timeSlots = Array.from({ length: 8 }, (_, i) => i + 8); // 8 AM to 3 PM

  const getBreakType = (hour) => {
    if (hour === 10) return "morningBreak";
    if (hour === 12) return "lunchBreak";
    if (hour === 15) return "eveningBreak";
    return null;
  };

  const renderBreakRow = (hour) => (
    <div
      key={`break-${hour}`}
      className="grid border-b last:border-b-0"
      style={{ gridTemplateColumns: "100px repeat(6, 1fr)" }}
    >
      {/* Time Column */}
      <div className="p-3 border-r bg-muted/20 flex flex-col items-center justify-center">
        <Skeleton className="h-4 w-12 mb-1" />
        <Skeleton className="h-3 w-10" />
      </div>

      {/* Break Columns */}
      {WEEKDAYS.map((day) => (
        <div
          key={`${day.key}-break-${hour}`}
          className="p-2 border-r last:border-r-0 min-h-[60px] bg-orange-50/50"
        >
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-8 h-8 rounded-full bg-orange-200 flex items-center justify-center mb-1">
                <Skeleton className="h-3 w-3 rounded-full" />
              </div>
              <Skeleton className="h-3 w-16 mx-auto mb-1" />
              <Skeleton className="h-2 w-12 mx-auto" />
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Timetable Grid Skeleton */}
      <Card className="p-0">
        <CardContent className="p-0">
          <div className="h-full overflow-x-auto">
            <div className="divide-x divide-border min-w-max h-full">
              {/* Header Row Skeleton */}
              <div
                className="grid border-b bg-muted/30"
                style={{ gridTemplateColumns: "100px repeat(6, 1fr)" }}
              >
                <div className="p-4 font-semibold text-center border-r">
                  <Clock className="h-4 w-4 mx-auto mb-1" />
                  <span className="text-sm">Time</span>
                </div>
                {WEEKDAYS.map((day) => (
                  <div
                    key={day.key}
                    className="p-4 text-center border-r last:border-r-0"
                  >
                    <Skeleton className="h-4 w-8 mx-auto mb-1" />
                    <Skeleton className="h-3 w-12 mx-auto mb-2" />
                    <Skeleton className="h-3 w-16 mx-auto" />
                  </div>
                ))}
              </div>

              {/* Time Slot Rows Skeleton */}
              {timeSlots.map((hour, slotIndex) => {
                const breakType = getBreakType(hour);

                // Render break row if this hour is a break time
                if (breakType) {
                  return renderBreakRow(hour);
                }

                // Render regular time slot
                return (
                  <div
                    key={hour}
                    className="grid border-b last:border-b-0"
                    style={{ gridTemplateColumns: "100px repeat(6, 1fr)" }}
                  >
                    {/* Time Column Skeleton */}
                    <div className="p-3 border-r bg-muted/20 flex flex-col items-center justify-center">
                      <Skeleton className="h-4 w-12 mb-1" />
                      <Skeleton className="h-3 w-10" />
                    </div>

                    {/* Day Columns Skeleton */}
                    {WEEKDAYS.map((day) => (
                      <div
                        key={`${day.key}-${hour}`}
                        className={`p-2 border-r last:border-r-0 min-h-[80px] ${
                          slotIndex % 2 === 0 ? "bg-muted/10" : ""
                        }`}
                      >
                        {/* Randomly show skeleton content for some slots */}
                        {Math.random() > 0.4 ? (
                          <div className="h-full">
                            <div className="rounded-lg p-3 h-full flex flex-col justify-between shadow-sm border-l-4 border-muted bg-card">
                              <div>
                                <div className="flex items-start gap-2 mb-2">
                                  <Skeleton className="h-3 w-3 rounded-full mt-0.5" />
                                  <div className="min-w-0 flex-1">
                                    <Skeleton className="h-4 w-full mb-1" />
                                    <Skeleton className="h-3 w-12" />
                                  </div>
                                </div>

                                <div className="flex items-center gap-1 mb-1">
                                  <Skeleton className="h-3 w-3 rounded-full" />
                                  <Skeleton className="h-3 w-20" />
                                </div>

                                <Skeleton className="h-3 w-16" />
                              </div>

                              <div className="mt-2 pt-2 border-t">
                                <Skeleton className="h-3 w-24" />
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="h-full flex items-center justify-center">
                            <div className="w-2 h-2 rounded-full bg-muted opacity-30"></div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Legend Skeleton */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between flex-wrap gap-4">
            <div className="flex items-center gap-4 flex-wrap">
              <div className="flex items-center gap-2">
                <Skeleton className="w-3 h-3 rounded-full" />
                <Skeleton className="h-3 w-16" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="w-3 h-3 rounded" />
                <Skeleton className="h-3 w-20" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="w-3 h-3 rounded" />
                <Skeleton className="h-3 w-20" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="w-3 h-3 rounded" />
                <Skeleton className="h-3 w-18" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="w-3 h-3 rounded" />
                <Skeleton className="h-3 w-20" />
              </div>
            </div>
            <Skeleton className="h-3 w-32" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
