import mongoose from "mongoose";

const StudentSchema = new mongoose.Schema(
  {
    // Personal Information
    firstName: {
      type: String,
      required: [true, "First name is required"],
      trim: true,
    },
    lastName: {
      type: String,
      required: [true, "Last name is required"],
      trim: true,
    },
    dateOfBirth: {
      type: Date,
      required: [true, "Date of birth is required"],
    },
    gender: {
      type: String,
      required: [true, "Gender is required"],
      trim: true,
    },
    bloodGroup: {
      type: String,
      trim: true,
    },
    nationality: {
      type: String,
      required: [true, "Nationality is required"],
      default: "Indian",
      trim: true,
    },
    religion: {
      type: String,
      trim: true,
    },
    parent: {
      id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Parent",
        required: [true, "Parent id is required"],
      },
      name: {
        type: String,
        required: [true, "Parent name is required"],
        trim: true,
      },
    },
    aadharNumber: {
      type: String,
      trim: true,
      validate: {
        validator: function (v) {
          return !v || /^\d{12}$/.test(v);
        },
        message: "Aadhar number must be 12 digits",
      },
    },
    studentId: {
      type: String,
      required: [true, "Student ID is required"],
      trim: true,
      unique: true,
    },
    rollNumber: {
      type: String,
      required: [true, "Roll number is required"],
      trim: true,
    },
    profilePhoto: {
      type: String,
      trim: true,
    },

    // Address Information
    address: {
      type: String,
      required: [true, "Address is required"],
      trim: true,
    },
    city: {
      type: String,
      required: [true, "City is required"],
      trim: true,
    },
    state: {
      type: String,
      required: [true, "State is required"],
      trim: true,
    },
    pincode: {
      type: String,
      required: [true, "Pincode is required"],
      trim: true,
      validate: {
        validator: function (v) {
          return /^\d{6}$/.test(v);
        },
        message: "Pincode must be 6 digits",
      },
    },
    country: {
      type: String,
      required: [true, "Country is required"],
      default: "India",
      trim: true,
    },

    // Contact Information
    personalEmail: {
      type: String,
      trim: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    phone: {
      type: String,
      trim: true,
    },
    alternatePhone: {
      type: String,
      trim: true,
    },
    preferredContactMethod: {
      type: String,
      enum: ["email", "phone", "parent_phone"],
      default: "email",
      trim: true,
    },

    // Academic Information
    class: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Class",
      required: [true, "Class is required"],
    },
    section: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Section",
      required: [true, "Section is required"],
    },
    academicYear: {
      type: String,
      required: [true, "Academic year is required"],
      trim: true,
    },
    admissionDate: {
      type: Date,
      required: [true, "Admission date is required"],
    },
    admissionType: {
      type: String,
      required: [true, "Admission type is required"],
      trim: true,
    },
    previousSchool: {
      type: String,
      trim: true,
    },

    // Emergency Contact
    emergencyContactName: {
      type: String,
      required: [true, "Emergency contact name is required"],
      trim: true,
    },
    emergencyContactPhone: {
      type: String,
      required: [true, "Emergency contact phone is required"],
      trim: true,
    },
    emergencyContactRelation: {
      type: String,
      required: [true, "Emergency contact relation is required"],
      trim: true,
    },

    // System Access & Permissions
    email: {
      type: String,
      required: [true, "System email is required"],
      trim: true,
      unique: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    password: {
      type: String,
      required: [true, "Password is required"],
      minlength: [8, "Password must be at least 8 characters"],
      select: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },

    // Fee & Financial Information
    feeStructure: {
      type: String,
      trim: true,
    },
    scholarshipApplied: {
      type: Boolean,
      default: false,
    },
    scholarshipAmount: {
      type: Number,
      min: [0, "Scholarship amount cannot be negative"],
    },
    transportRequired: {
      type: Boolean,
      default: false,
    },
    transportMode: {
      type: String,
      trim: true,
    },
    busRoute: {
      type: String,
      trim: true,
    },

    // Medical Information
    medicalConditions: {
      type: String,
      trim: true,
    },
    allergies: {
      type: String,
      trim: true,
    },
    medications: {
      type: String,
      trim: true,
    },
    doctorName: {
      type: String,
      trim: true,
    },
    doctorPhone: {
      type: String,
      trim: true,
    },
    medicalCertificate: {
      type: String,
      trim: true,
    },

    // Additional Information
    hobbies: {
      type: String,
      trim: true,
    },
    achievements: {
      type: String,
      trim: true,
    },
    specialNeeds: {
      type: String,
      trim: true,
    },
    additionalNotes: {
      type: String,
      trim: true,
    },

    // Status
    status: {
      type: String,
      enum: ["active", "inactive", "on-leave"],
      default: "active",
    },

    // Document Uploads
    birthCertificate: {
      type: String,
      trim: true,
    },
    addressProof: {
      type: String,
      trim: true,
    },

    // School Reference
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },

    // Academic Performance References
    subjects: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Subject",
      },
    ],

    // Attendance tracking
    attendanceRecords: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Attendance",
      },
    ],

    // Grades and assessment records
    gradeRecords: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Grade",
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Indexes for better performance
StudentSchema.index({ studentId: 1, schoolId: 1 });
StudentSchema.index({ class: 1, section: 1 });
StudentSchema.index({ parent: 1 });
StudentSchema.index({ status: 1 });

export default mongoose.model("Student", StudentSchema);
