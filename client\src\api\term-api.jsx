import axiosInstance from "./axios-instance";

export const createTerm = async (termData) => {
  try {
    const response = await axiosInstance.post("/terms/create", termData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating term:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create term");
  }
};

export const getTerms = async () => {
  try {
    const response = await axiosInstance.get("/terms");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching terms:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch terms");
  }
};

export const getTermById = async (id) => {
  try {
    const response = await axiosInstance.get(`/terms/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching term with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch term with ID ${id}`)
    );
  }
};

export const updateTerm = async (id, termData) => {
  try {
    const response = await axiosInstance.put(`/terms/${id}`, termData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating term with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to update term with ID ${id}`)
    );
  }
};

export const deleteTerm = async (id) => {
  try {
    const response = await axiosInstance.delete(`/terms/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting term with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to delete term with ID ${id}`)
    );
  }
};

export const updateTermStatus = async (id, status) => {
  try {
    const response = await axiosInstance.patch(`/terms/${id}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating term status for term with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update term status for term with ID ${id}`)
    );
  }
};
