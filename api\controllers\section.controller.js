import Section from "../models/section.model.js";
import Class from "../models/class.model.js";

// Create a new section
export const createSection = async (req, res) => {
  try {
    const sectionData = req.body;

    if (!sectionData.schoolId && req.user?.schoolId) {
      sectionData.schoolId = req.user.schoolId;
    }

    const newSection = new Section(sectionData);
    await newSection.save();

    if (newSection.classId) {
      await Class.findByIdAndUpdate(
        newSection.classId,
        { $push: { sections: newSection._id } },
        { new: true }
      );
    }

    res.status(201).json({
      success: true,
      message: "Section created successfully",
      data: newSection,
    });
  } catch (error) {
    console.error("Create Section Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Section code already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all sections
export const getAllSections = async (req, res) => {
  try {
    const { schoolId, classId, isActive } = req.query;

    let query = {};

    // Apply filters
    if (schoolId) {
      query.schoolId = schoolId;
    } else if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    if (classId) {
      query.classId = classId;
    }

    if (isActive !== undefined) {
      query.isActive = isActive === "true";
    }

    const sections = await Section.find(query)
      .sort({ createdAt: -1 })
      .populate("classId", "name code")
      .populate("schoolId", "name");

    res.status(200).json({
      success: true,
      count: sections.length,
      data: sections,
    });
  } catch (error) {
    console.error("Get All Sections Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get section by ID
export const getSectionById = async (req, res) => {
  try {
    const sectionItem = await Section.findById(req.params.id)
      .populate("classId", "name code")
      .populate("schoolId", "name");

    if (!sectionItem) {
      return res.status(404).json({
        success: false,
        message: "Section not found",
      });
    }

    res.status(200).json({
      success: true,
      data: sectionItem,
    });
  } catch (error) {
    console.error("Get Section By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update section
export const updateSection = async (req, res) => {
  try {
    const sectionData = req.body;

    const sectionItem = await Section.findByIdAndUpdate(
      req.params.id,
      sectionData,
      {
        new: true,
        runValidators: true,
      }
    ).populate("classId", "name code");

    if (!sectionItem) {
      return res.status(404).json({
        success: false,
        message: "Section not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Section updated successfully",
      data: sectionItem,
    });
  } catch (error) {
    console.error("Update Section Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Section code already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete section
export const deleteSection = async (req, res) => {
  try {
    const sectionItem = await Section.findByIdAndDelete(req.params.id);

    if (!sectionItem) {
      return res.status(404).json({
        success: false,
        message: "Section not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Section deleted successfully",
    });
  } catch (error) {
    console.error("Delete Section Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update section status (active/inactive)
export const updateSectionStatus = async (req, res) => {
  try {
    const { isActive } = req.body;

    if (isActive === undefined) {
      return res.status(400).json({
        success: false,
        message: "Please provide isActive status",
      });
    }

    const sectionItem = await Section.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true, runValidators: true }
    );

    if (!sectionItem) {
      return res.status(404).json({
        success: false,
        message: "Section not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Section status updated successfully",
      data: sectionItem,
    });
  } catch (error) {
    console.error("Update Section Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get sections by class
export const getSectionsByClass = async (req, res) => {
  try {
    const { classId } = req.params;
    const sections = await Section.find({
      classId,
      ...(req.user?.schoolId && { schoolId: req.user.schoolId }),
    }).sort({ name: 1 });

    res.status(200).json({
      success: true,
      count: sections.length,
      data: sections,
    });
  } catch (error) {
    console.error("Get Sections By Class Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
