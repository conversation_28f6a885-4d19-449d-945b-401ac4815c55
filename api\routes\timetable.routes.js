import express from "express";
import {
  createTimetable,
  getAllTimetables,
  getTimetableById,
  updateTimetable,
  deleteTimetable,
  updateTimetableStatus,
  getTimetableByClassSection,
  getTeacherTimetable,
} from "../controllers/timetable.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createTimetable
);

router.get(
  "/",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getAllTimetables
);

router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getTimetableById
);

router.put(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  updateTimetable
);

router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteTimetable
);

router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateTimetableStatus
);

router.get(
  "/class/:classId/section/:sectionId",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getTimetableByClassSection
);

router.get(
  "/teacher/:teacherId",
  protect,
  authorize(["admin", "school-admin", "teacher"]),
  getTeacherTimetable
);

export default router;
