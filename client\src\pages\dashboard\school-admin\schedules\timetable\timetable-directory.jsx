import React, { useEffect, useState } from "react";
import { PageHeader } from "@/components/dashboard/page-header";
import { PlusCircle, Calendar, CalendarDays } from "lucide-react";
import { toast } from "sonner";
import { StatCard } from "@/components/dashboard/stat-card";
import { useTimetable } from "@/context/timetable-context";
import { useTeacher } from "@/context/teacher-context";
import { useSubject } from "@/context/subject-context";
import { useClass } from "@/context/class-context";
import { useSection } from "@/context/section-context";
import { TimetableSkeleton } from "@/components/timetable/timetable-skeleton/timetable-skeleton";
import { Timetable } from "@/components/timetable/timetable-component/timetable";
import { Card, CardContent } from "@/components/ui/card";

const TimetableDirectory = () => {
  const { fetchAllTimetables, timetables, isLoading } = useTimetable();
  const { fetchTeacherById } = useTeacher();
  const { fetchSubjectById } = useSubject();
  const { fetchClassById } = useClass();
  const { fetchSectionById } = useSection();

  const [selectedTimetableId, setSelectedTimetableId] = useState(null);
  const [teacherNames, setTeacherNames] = useState({});
  const [subjectDetails, setSubjectDetails] = useState({});
  const [classDetails, setClassDetails] = useState({});
  const [sectionDetails, setSectionDetails] = useState({});

  const currentTimetable = selectedTimetableId
    ? timetables?.find((t) => (t.id || t._id) === selectedTimetableId)
    : timetables?.[0];

  useEffect(() => {
    fetchAllTimetables();
  }, []);

  useEffect(() => {
    if (timetables?.length > 0 && !selectedTimetableId) {
      setSelectedTimetableId(timetables[0].id || timetables[0]._id);
    }
  }, [timetables, selectedTimetableId]);

  const handleTimetableSelect = (timetableId) => {
    setSelectedTimetableId(timetableId);
    setTeacherNames({});
    setSubjectDetails({});
    setClassDetails({});
    setSectionDetails({});
  };

  useEffect(() => {
    const loadSlotDetails = async () => {
      if (!currentTimetable?.timetable) return;

      const allSlots = Object.values(currentTimetable.timetable).flat();

      for (const slot of allSlots) {
        // Load teacher details
        if (slot.teacherId && !teacherNames[slot.teacherId]) {
          try {
            const teacher = await fetchTeacherById(slot.teacherId);
            setTeacherNames((prev) => ({
              ...prev,
              [slot.teacherId]: `${teacher.firstName} ${teacher.lastName}`,
            }));
          } catch (error) {
            console.error(`Error loading teacher ${slot.teacherId}:`, error);
            toast.error(error.message || "Failed to load teacher");
          }
        }

        // Load subject details
        if (slot.subjectId && !subjectDetails[slot.subjectId]) {
          try {
            const subject = await fetchSubjectById(slot.subjectId);
            setSubjectDetails((prev) => ({
              ...prev,
              [slot.subjectId]: {
                name: subject.name,
                code: subject.code,
              },
            }));
          } catch (error) {
            console.error(`Error loading subject ${slot.subjectId}:`, error);
            toast.error(error.message || "Failed to load subject");
          }
        }

        // Load class details
        if (slot.classId && !classDetails[slot.classId]) {
          try {
            const classData = await fetchClassById(slot.classId);
            setClassDetails((prev) => ({
              ...prev,
              [slot.classId]: {
                name: classData.name,
                code: classData.code,
              },
            }));
          } catch (error) {
            console.error(`Error loading class ${slot.classId}:`, error);
            toast.error(error.message || "Failed to load class");
          }
        }

        // Load section details
        if (slot.sectionId && !sectionDetails[slot.sectionId]) {
          try {
            const section = await fetchSectionById(slot.sectionId);
            setSectionDetails((prev) => ({
              ...prev,
              [slot.sectionId]: {
                name: section.name,
                code: section.code,
              },
            }));
          } catch (error) {
            console.error(`Error loading section ${slot.sectionId}:`, error);
            toast.error(error.message || "Failed to load section");
          }
        }
      }
    };

    loadSlotDetails();
  }, [currentTimetable]);

  const weeklyClasses = currentTimetable?.timetable
    ? Object.values(currentTimetable.timetable)
        .filter((daySlots) => daySlots.length > 0)
        .reduce((total, daySlots) => total + daySlots.length, 0)
    : 0;

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Timetable Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Schedule", href: "/dashboard/schedule" },
            { label: "Timetables" },
          ]}
          actions={[
            {
              label: "New Timetable",
              icon: PlusCircle,
              href: "/dashboard/schedule/timetable/create",
            },
          ]}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          <StatCard
            title="Total Timetables"
            value={timetables?.length || 0}
            description="Active timetables"
            icon={Calendar}
            isLoading={isLoading}
          />
          <StatCard
            title="Weekly Classes"
            value={weeklyClasses}
            description="Total weekday classes"
            icon={Calendar}
            isLoading={isLoading}
          />
          <StatCard
            title="Active Teachers"
            value={Object.keys(teacherNames).length}
            description="In current timetable"
            icon={Calendar}
            isLoading={isLoading}
          />
          <StatCard
            title="Active Subjects"
            value={Object.keys(subjectDetails).length}
            description="In current timetable"
            icon={Calendar}
            isLoading={isLoading}
          />
        </div>

        {/* Timetable Display */}
        <div className="space-y-6">
          {isLoading ? (
            <TimetableSkeleton />
          ) : (
            <>
              {!timetables && timetables.length === 0 ? (
                <Card>
                  <CardContent className="p-8 text-center">
                    <div className="text-muted-foreground">
                      <CalendarDays className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <h3 className="text-lg font-medium mb-2">No Timetable</h3>
                      <p>You don't have any timetables assigned.</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Timetable
                  data={currentTimetable}
                  timetableData={currentTimetable}
                  teacherNames={teacherNames}
                  subjectDetails={subjectDetails}
                  classDetails={classDetails}
                  sectionDetails={sectionDetails}
                  isLoading={isLoading}
                  timetables={timetables}
                  showHeader={true}
                  onTimetableSelect={handleTimetableSelect}
                  selectedTimetableId={selectedTimetableId}
                />
              )}
            </>
          )}
        </div>
      </main>
    </div>
  );
};

export default TimetableDirectory;
