import Term from "../models/term.model.js";

// Create a new term
export const createTerm = async (req, res) => {
  try {
    const termData = req.body;

    if (!termData.schoolId && req.user?.schoolId) {
      termData.schoolId = req.user.schoolId;
    }

    const newTerm = new Term(termData);
    await newTerm.save();

    res.status(201).json({
      success: true,
      message: "Term created successfully",
      data: newTerm,
    });
  } catch (error) {
    console.error("Create Term Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Term code already exists in this school",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all terms
export const getAllTerms = async (req, res) => {
  try {
    const { schoolId, academicYear, isActive } = req.query;
    let query = {};

    // Apply filters
    if (schoolId) {
      query.schoolId = schoolId;
    } else if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }
    if (academicYear) {
      query.academicYear = academicYear;
    }
    if (isActive !== undefined) {
      query.isActive = isActive === "true";
    }

    const terms = await Term.find(query)
      .sort({ createdAt: -1 })
      .populate("schoolId", "name");

    res.status(200).json({
      success: true,
      count: terms.length,
      data: terms,
    });
  } catch (error) {
    console.error("Get All Terms Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get term by ID
export const getTermById = async (req, res) => {
  try {
    const term = await Term.findById(req.params.id).populate(
      "schoolId",
      "name"
    );
    if (!term) {
      return res.status(404).json({
        success: false,
        message: "Term not found",
      });
    }
    res.status(200).json({
      success: true,
      data: term,
    });
  } catch (error) {
    console.error("Get Term By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update term
export const updateTerm = async (req, res) => {
  try {
    const termData = req.body;
    const term = await Term.findByIdAndUpdate(req.params.id, termData, {
      new: true,
      runValidators: true,
    }).populate("schoolId", "name");
    if (!term) {
      return res.status(404).json({
        success: false,
        message: "Term not found",
      });
    }
    res.status(200).json({
      success: true,
      message: "Term updated successfully",
      data: term,
    });
  } catch (error) {
    console.error("Update Term Error:", error);
    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Term code already exists in this school",
      });
    }
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete term
export const deleteTerm = async (req, res) => {
  try {
    const term = await Term.findByIdAndDelete(req.params.id);
    if (!term) {
      return res.status(404).json({
        success: false,
        message: "Term not found",
      });
    }
    res.status(200).json({
      success: true,
      message: "Term deleted successfully",
    });
  } catch (error) {
    console.error("Delete Term Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update term status (active/inactive)
export const updateTermStatus = async (req, res) => {
  try {
    const { status } = req.body;
    const term = await Term.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    );
    if (!term) {
      return res.status(404).json({
        success: false,
        message: "Term not found",
      });
    }
    res.status(200).json({
      success: true,
      message: `Term status updated to ${status}`,
      data: term,
    });
  } catch (error) {
    console.error("Update Term Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
