import React from "react";
import { TimetableContent } from "@/components/timetable/timetable-component/timetable-content";
import { TimetableHeader } from "@/components/timetable/timetable-component/timetable-header";

export function Timetable({
  data = {},
  timetableData,
  teacherNames = {},
  subjectDetails = {},
  classDetails = {},
  sectionDetails = {},
  isLoading = false,
  timetables = [],
  onTimetableSelect,
  selectedTimetableId,
  // Configuration options
  mode = "default",
  showBreaks = true,
  showClassCounts = true,
  showTeacherInfo = true,
  showHeader = false,
}) {
  return (
    <>
      {showHeader && (
        <TimetableHeader
          data={data}
          timetables={timetables}
          selectedTimetableId={selectedTimetableId}
          onTimetableSelect={onTimetableSelect}
        />
      )}
      <div className="w-full">
        <TimetableContent
          data={data || timetableData}
          timetableData={timetableData}
          teacherNames={teacherNames}
          subjectDetails={subjectDetails}
          classDetails={classDetails}
          sectionDetails={sectionDetails}
          isLoading={isLoading}
          mode={mode}
          showBreaks={showBreaks}
          showClassCounts={showClassCounts}
          showTeacherInfo={showTeacherInfo}
        />
      </div>
    </>
  );
}
