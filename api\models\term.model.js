import mongoose from "mongoose";

const TermSchema = new mongoose.Schema(
  {
    // General details
    name: {
      type: String,
      required: [true, "Term name is required"],
      trim: true,
    },
    code: {
      type: String,
      required: [true, "Term code is required"],
      trim: true,
      minlength: [2, "Code must be at least 2 characters"],
      maxlength: [10, "Code must not exceed 10 characters"],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, "Description must not exceed 500 characters"],
    },

    // Academic details
    academicYear: {
      type: String,
      required: [true, "Academic year is required"],
      trim: true,
    },
    startDate: {
      type: Date,
      required: [true, "Start date is required"],
    },
    endDate: {
      type: Date,
      required: [true, "End date is required"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },

    // Additional information
    notes: {
      type: String,
      trim: true,
    },

    // Relationships
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },

    // Status
    status: {
      type: String,
      enum: ["active", "inactive"],
      default: "active",
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
TermSchema.index({ schoolId: 1 });
TermSchema.index({ code: 1, schoolId: 1 }, { unique: true });
TermSchema.index({ name: 1, schoolId: 1 });
TermSchema.index({ academicYear: 1 });

export default mongoose.model("Term", TermSchema);
