import { createContext, useContext, useState } from "react";
import {
  createSection,
  getSections,
  getSectionById,
  updateSection,
  deleteSection,
  updateSectionStatus,
  getSectionsByClass,
} from "@/api/section-api";

const SectionContext = createContext({
  sections: [],
  currentSection: null,
  isLoading: false,
  error: null,
  addSection: () => {},
  fetchAllSections: () => {},
  fetchSectionById: () => {},
  editSection: () => {},
  removeSection: () => {},
  updateStatus: () => {},
  fetchSectionsByClass: () => {},
});

export const SectionProvider = ({ children }) => {
  const [sections, setSections] = useState([]);
  const [currentSection, setCurrentSection] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addSection = async (sectionData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createSection(sectionData);
      setSections((prevSections) => [...prevSections, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create section");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllSections = async (filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSections(filters);
      setSections(response.data);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to fetch sections");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchSectionById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSectionById(id);
      setCurrentSection(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch section with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editSection = async (id, sectionData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateSection(id, sectionData);
      setSections(
        sections.map((sec) => (sec._id === id ? response.data : sec))
      );
      if (currentSection && currentSection._id === id) {
        setCurrentSection(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update section with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeSection = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteSection(id);
      setSections(sections.filter((sec) => sec._id !== id));
      if (currentSection && currentSection._id === id) {
        setCurrentSection(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete section with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, isActive) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateSectionStatus(id, isActive);
      setSections(
        sections.map((sec) => (sec._id === id ? response.data : sec))
      );
      if (currentSection && currentSection._id === id) {
        setCurrentSection(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(
        error.message || `Failed to update section status with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const fetchSectionsByClass = async (classId) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSectionsByClass(classId);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(
        error.message || `Failed to fetch sections for class: ${classId}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const sectionOptions = sections.map((sec) => ({
    label: `${sec.name} (${sec.code})`,
    value: sec._id,
    id: sec._id,
    name: sec.name,
    code: sec.code,
    classId: sec.classId,
  }));

  return (
    <SectionContext.Provider
      value={{
        sections,
        setSections,
        currentSection,
        isLoading,
        error,
        addSection,
        fetchAllSections,
        fetchSectionById,
        editSection,
        removeSection,
        updateStatus,
        sectionOptions,
        fetchSectionsByClass,
      }}
    >
      {children}
    </SectionContext.Provider>
  );
};

export const useSection = () => useContext(SectionContext);
