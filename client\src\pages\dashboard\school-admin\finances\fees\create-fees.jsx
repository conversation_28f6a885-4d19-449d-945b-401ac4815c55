import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { FeeForm } from "@/components/forms/dashboard/fees/fee-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useFee } from "@/context/fee-context";
import { toast } from "sonner";

const CreateFees = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchFeeById, isLoading } = useFee();
  const [feeData, setFeeData] = useState(null);
  const [loading, setLoading] = useState(!!id);

  useEffect(() => {
    const loadFeeData = async () => {
      if (id) {
        try {
          const data = await fetchFeeById(id);
          setFeeData(data);
        } catch (error) {
          console.error("Failed to fetch fee data:", error);
          toast.error("Failed to load fee data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadFeeData();
  }, [id, fetchFeeById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Fee" : "Create New Fee"}
        actions={[
          {
            label: "Back to Fees",
            href: "/dashboard/finances/fees",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Finances", href: "/dashboard/finances" },
          { label: "Fees", href: "/dashboard/finances/fees" },
          { label: id ? "Edit Fee" : "Create Fee" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <FeeForm editingId={id} initialData={feeData} />
      )}
    </Container>
  );
};

export default CreateFees;
