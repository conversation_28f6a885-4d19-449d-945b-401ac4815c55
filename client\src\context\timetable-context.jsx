import { createContext, useContext, useState } from "react";
import {
  createTimetable,
  getAllTimetables,
  getTimetableById,
  updateTimetable,
  deleteTimetable,
  updateTimetableStatus,
  getTimetableByClassSection,
  getTeacherTimetable,
} from "@/api/timetable-api";

const TimetableContext = createContext({
  timetables: [],
  currentTimetable: null,
  isLoading: false,
  error: null,
  addTimetable: () => {},
  fetchAllTimetables: () => {},
  fetchTimetableById: () => {},
  editTimetable: () => {},
  removeTimetable: () => {},
  updateStatus: () => {},
  fetchTimetableByClassSection: () => {},
  fetchTeacherTimetable: () => {},
  getAggregatedTeacherTimetable: () => {},
  checkConflicts: () => {},
  duplicateTimetable: () => {},
});

export const TimetableProvider = ({ children }) => {
  const [timetables, setTimetables] = useState([]);
  const [currentTimetable, setCurrentTimetable] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addTimetable = async (timetableData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createTimetable(timetableData);
      setTimetables((prevTimetables) => [response.data, ...prevTimetables]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create timetable");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllTimetables = async (filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getAllTimetables(filters);
      setTimetables(response.data || []);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to fetch timetables");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchTimetableById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getTimetableById(id);
      setCurrentTimetable(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch timetable with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editTimetable = async (id, timetableData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateTimetable(id, timetableData);
      setTimetables(
        timetables.map((timetable) =>
          timetable._id === id ? response.data : timetable
        )
      );
      if (currentTimetable && currentTimetable._id === id) {
        setCurrentTimetable(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update timetable with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeTimetable = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteTimetable(id);
      setTimetables(timetables.filter((timetable) => timetable._id !== id));
      if (currentTimetable && currentTimetable._id === id) {
        setCurrentTimetable(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete timetable with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, status) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateTimetableStatus(id, status);
      setTimetables(
        timetables.map((timetable) =>
          timetable._id === id ? response.data : timetable
        )
      );
      if (currentTimetable && currentTimetable._id === id) {
        setCurrentTimetable(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(
        error.message || `Failed to update timetable status with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const fetchTimetableByClassSection = async (
    classId,
    sectionId,
    filters = {}
  ) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getTimetableByClassSection(
        classId,
        sectionId,
        filters
      );
      setCurrentTimetable(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(
        error.message ||
          `Failed to fetch timetable for class: ${classId}, section: ${sectionId}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const fetchTeacherTimetable = async (teacherId, filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getTeacherTimetable(teacherId, filters);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(
        error.message || `Failed to fetch timetable for teacher: ${teacherId}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  // Create timetable options for select inputs
  const timetableOptions = timetables.map((timetable) => ({
    label: timetable.name,
    value: {
      id: timetable._id,
      name: timetable.name,
    },
    id: timetable._id,
    name: timetable.name,
    classId: timetable.classId,
    sectionId: timetable.sectionId,
    termId: timetable.termId,
    status: timetable.status,
    academicYear: timetable.academicYear,
  }));

  // Helper function to get timetable periods for a specific day
  const getTimetablePeriods = (timetable, day) => {
    return timetable?.timetable?.[day] || [];
  };

  // Helper function to format timetable for display
  const formatTimetableForDisplay = (timetable) => {
    if (!timetable) return null;

    const days = [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
      "sunday",
    ];
    const formattedTimetable = {};

    days.forEach((day) => {
      formattedTimetable[day] = getTimetablePeriods(timetable, day).map(
        (period) => ({
          ...period,
          subject: period.subjectId?.name || "Unknown Subject",
          subjectCode: period.subjectId?.code || "",
          teacher: period.teacherId?.name || "Unknown Teacher",
          teacherEmail: period.teacherId?.email || "",
        })
      );
    });

    return {
      ...timetable,
      formattedTimetable,
    };
  };

  // Create aggregated teacher timetable from all timetables
  const getAggregatedTeacherTimetable = (teacherTimetables, currentTeacher) => {
    if (!teacherTimetables || !currentTeacher) return null;

    const days = [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
      "sunday",
    ];

    const aggregatedSchedule = {};
    days.forEach((day) => {
      aggregatedSchedule[day] = [];
    });

    // Aggregate periods from all timetables where teacher is assigned
    teacherTimetables.forEach((timetable) => {
      if (timetable.timetable) {
        days.forEach((day) => {
          const dayPeriods = timetable.timetable[day] || [];
          const teacherPeriods = dayPeriods.filter(
            (period) =>
              period.teacherId &&
              period.teacherId.toString() === currentTeacher._id.toString()
          );

          // Add class and section info to each period
          teacherPeriods.forEach((period) => {
            aggregatedSchedule[day].push({
              ...period,
              className: timetable.classId?.name || "Unknown Class",
              sectionName: timetable.sectionId?.name || "",
              timetableName: timetable.name,
              termName: timetable.termId?.name || "",
              academicYear: timetable.academicYear || "",
            });
          });
        });
      }
    });

    // Sort periods by start time for each day
    days.forEach((day) => {
      aggregatedSchedule[day].sort((a, b) => {
        return a.startTime.localeCompare(b.startTime);
      });
    });

    const firstTimetable = teacherTimetables[0];
    return {
      name: "My Teaching Schedule",
      classId: { name: "All Classes" },
      sectionId: { name: "All Sections" },
      termId: { name: firstTimetable?.termId?.name || "Current Term" },
      academicYear: firstTimetable?.academicYear || "Current Year",
      timetable: aggregatedSchedule,
    };
  };

  return (
    <TimetableContext.Provider
      value={{
        timetables,
        setTimetables,
        currentTimetable,
        setCurrentTimetable,
        isLoading,
        error,
        addTimetable,
        fetchAllTimetables,
        fetchTimetableById,
        editTimetable,
        removeTimetable,
        updateStatus,
        fetchTimetableByClassSection,
        fetchTeacherTimetable,
        getAggregatedTeacherTimetable,
        timetableOptions,
        getTimetablePeriods,
        formatTimetableForDisplay,
      }}
    >
      {children}
    </TimetableContext.Provider>
  );
};

export const useTimetable = () => useContext(TimetableContext);
