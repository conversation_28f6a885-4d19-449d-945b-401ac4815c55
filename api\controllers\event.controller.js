import Event from "../models/event.model.js";
import Notification from "../models/notification.model.js";

// Helper function to create event notification
const createEventNotification = async (event, action = "created", user) => {
  try {
    let title, message, recipients;

    switch (action) {
      case "created":
        title = `New Event: ${event.title}`;
        message = `A new event "${
          event.title
        }" has been scheduled for ${new Date(
          event.startDate
        ).toLocaleDateString()}. ${event.description}`;
        break;
      case "updated":
        title = `Event Updated: ${event.title}`;
        message = `The event "${event.title}" has been updated. Please check the details.`;
        break;
      case "cancelled":
        title = `Event Cancelled: ${event.title}`;
        message = `The event "${event.title}" scheduled for ${new Date(
          event.startDate
        ).toLocaleDateString()} has been cancelled.`;
        break;
      case "reminder":
        title = `Event Reminder: ${event.title}`;
        message = `Reminder: The event "${
          event.title
        }" is scheduled for ${new Date(event.startDate).toLocaleDateString()}.`;
        break;
      default:
        title = `Event Notification: ${event.title}`;
        message = `There's an update regarding the event "${event.title}".`;
    }

    // Determine recipients based on attendee type
    switch (event.attendeeType) {
      case "all_students":
        recipients = "all-students";
        break;
      case "teachers_only":
        recipients = "all-teachers";
        break;
      case "parents_only":
        recipients = "all-parents";
        break;
      case "staff_only":
        recipients = "all-admins";
        break;
      case "specific_classes":
        recipients = "all-students";
        break;
      default:
        recipients = "all-users";
    }

    const notification = new Notification({
      title,
      message,
      type: "Event",
      priority:
        event.priority === "urgent"
          ? "High"
          : event.priority === "high"
          ? "High"
          : "Medium",
      recipients,
      scheduleType: "immediate",
      createdBy: user.id,
      createdByName: `${user.firstName} ${user.lastName}`,
      createdByRole: user.role,
      createdByEmail: user.email,
      schoolId: event.schoolId,
    });

    await notification.save();
    return notification;
  } catch (error) {
    console.error("Error creating event notification:", error);
  }
};

// Create a new event
export const createEvent = async (req, res) => {
  try {
    const eventData = req.body;

    // Set schoolId and createdBy from authenticated user
    if (!eventData.schoolId && req.user?.schoolId) {
      eventData.schoolId = req.user.schoolId;
    }
    if (req.user?.id) {
      eventData.createdBy = req.user.id;
    }

    const newEvent = new Event(eventData);
    await newEvent.save();

    // Populate relationships for response
    await newEvent.populate([
      { path: "organizer", select: "firstName lastName email" },
      { path: "coordinator", select: "firstName lastName email" },
      { path: "targetClasses", select: "name code" },
      { path: "targetSections", select: "name" },
      { path: "schoolId", select: "name" },
      { path: "createdBy", select: "firstName lastName email" },
    ]);

    // Create notification for the new event
    if (req.user) {
      await createEventNotification(newEvent, "created", req.user);
    }

    res.status(201).json({
      success: true,
      message: "Event created successfully",
      data: newEvent,
    });
  } catch (error) {
    console.error("Create Event Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Event with similar details already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all events
export const getAllEvents = async (req, res) => {
  try {
    const {
      schoolId,
      eventType,
      status,
      priority,
      venueType,
      attendeeType,
      startDate,
      endDate,
      isActive,
      page = 1,
      limit = 10,
    } = req.query;

    let query = {};

    // Apply filters
    if (schoolId) {
      query.schoolId = schoolId;
    } else if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    if (eventType) {
      query.eventType = eventType;
    }

    if (status) {
      query.status = status;
    }

    if (priority) {
      query.priority = priority;
    }

    if (venueType) {
      query.venueType = venueType;
    }

    if (attendeeType) {
      query.attendeeType = attendeeType;
    }

    if (startDate && endDate) {
      query.startDate = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    } else if (startDate) {
      query.startDate = { $gte: new Date(startDate) };
    } else if (endDate) {
      query.endDate = { $lte: new Date(endDate) };
    }

    if (isActive !== undefined) {
      query.isActive = isActive === "true";
    }

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const events = await Event.find(query)
      .sort({ startDate: 1, createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .populate([
        { path: "organizer", select: "firstName lastName email" },
        { path: "coordinator", select: "firstName lastName email" },
        { path: "targetClasses", select: "name code" },
        { path: "targetSections", select: "name" },
        { path: "schoolId", select: "name" },
        { path: "createdBy", select: "firstName lastName email" },
      ]);

    const totalEvents = await Event.countDocuments(query);

    res.status(200).json({
      success: true,
      count: events.length,
      total: totalEvents,
      page: pageNum,
      pages: Math.ceil(totalEvents / limitNum),
      data: events,
    });
  } catch (error) {
    console.error("Get All Events Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get event by ID
export const getEventById = async (req, res) => {
  try {
    const event = await Event.findById(req.params.id).populate([
      { path: "organizer", select: "firstName lastName email phone" },
      { path: "coordinator", select: "firstName lastName email phone" },
      { path: "targetClasses", select: "name code grade" },
      { path: "targetSections", select: "name" },
      { path: "schoolId", select: "name address phone email" },
      { path: "createdBy", select: "firstName lastName email" },
    ]);

    if (!event) {
      return res.status(404).json({
        success: false,
        message: "Event not found",
      });
    }

    res.status(200).json({
      success: true,
      data: event,
    });
  } catch (error) {
    console.error("Get Event By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update event
export const updateEvent = async (req, res) => {
  try {
    const eventData = req.body;

    const event = await Event.findByIdAndUpdate(req.params.id, eventData, {
      new: true,
      runValidators: true,
    }).populate([
      { path: "organizer", select: "firstName lastName email" },
      { path: "coordinator", select: "firstName lastName email" },
      { path: "targetClasses", select: "name code" },
      { path: "targetSections", select: "name" },
      { path: "schoolId", select: "name" },
      { path: "createdBy", select: "firstName lastName email" },
    ]);

    if (!event) {
      return res.status(404).json({
        success: false,
        message: "Event not found",
      });
    }

    // Create notification for the updated event
    if (req.user) {
      await createEventNotification(event, "updated", req.user);
    }

    res.status(200).json({
      success: true,
      message: "Event updated successfully",
      data: event,
    });
  } catch (error) {
    console.error("Update Event Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: "Event with similar details already exists",
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete event
export const deleteEvent = async (req, res) => {
  try {
    const event = await Event.findByIdAndDelete(req.params.id);

    if (!event) {
      return res.status(404).json({
        success: false,
        message: "Event not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Event deleted successfully",
    });
  } catch (error) {
    console.error("Delete Event Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update event status
export const updateEventStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: "Please provide event status",
      });
    }

    const event = await Event.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    ).populate([
      { path: "organizer", select: "firstName lastName email" },
      { path: "coordinator", select: "firstName lastName email" },
      { path: "targetClasses", select: "name code" },
      { path: "targetSections", select: "name" },
    ]);

    if (!event) {
      return res.status(404).json({
        success: false,
        message: "Event not found",
      });
    }

    // Create notification if event is cancelled
    if (status === "cancelled" && req.user) {
      await createEventNotification(event, "cancelled", req.user);
    }

    res.status(200).json({
      success: true,
      message: "Event status updated successfully",
      data: event,
    });
  } catch (error) {
    console.error("Update Event Status Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update event active status
export const updateEventActiveStatus = async (req, res) => {
  try {
    const { isActive } = req.body;

    if (isActive === undefined) {
      return res.status(400).json({
        success: false,
        message: "Please provide isActive status",
      });
    }

    const event = await Event.findByIdAndUpdate(
      req.params.id,
      { isActive },
      { new: true, runValidators: true }
    );

    if (!event) {
      return res.status(404).json({
        success: false,
        message: "Event not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Event active status updated successfully",
      data: event,
    });
  } catch (error) {
    console.error("Update Event Active Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get events by date range
export const getEventsByDateRange = async (req, res) => {
  try {
    const { startDate, endDate } = req.params;

    let query = {
      $or: [
        {
          startDate: {
            $gte: new Date(startDate),
            $lte: new Date(endDate),
          },
        },
        {
          endDate: {
            $gte: new Date(startDate),
            $lte: new Date(endDate),
          },
        },
        {
          $and: [
            { startDate: { $lte: new Date(startDate) } },
            { endDate: { $gte: new Date(endDate) } },
          ],
        },
      ],
    };

    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    const events = await Event.find(query)
      .sort({ startDate: 1 })
      .populate([
        { path: "organizer", select: "firstName lastName" },
        { path: "coordinator", select: "firstName lastName" },
        { path: "targetClasses", select: "name code" },
        { path: "targetSections", select: "name" },
      ]);

    res.status(200).json({
      success: true,
      count: events.length,
      data: events,
    });
  } catch (error) {
    console.error("Get Events By Date Range Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get upcoming events
export const getUpcomingEvents = async (req, res) => {
  try {
    const { limit = 5 } = req.query;
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    let query = {
      startDate: { $gte: today },
      status: { $nin: ["cancelled", "completed"] },
      isActive: true,
    };

    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    const events = await Event.find(query)
      .sort({ startDate: 1 })
      .limit(parseInt(limit))
      .populate([
        { path: "organizer", select: "firstName lastName" },
        { path: "coordinator", select: "firstName lastName" },
      ])
      .select(
        "title startDate endDate eventType priority status venueType venueName"
      );

    res.status(200).json({
      success: true,
      count: events.length,
      data: events,
    });
  } catch (error) {
    console.error("Get Upcoming Events Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get events by type
export const getEventsByType = async (req, res) => {
  try {
    const { eventType } = req.params;

    let query = { eventType };

    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    const events = await Event.find(query)
      .sort({ startDate: 1 })
      .populate([
        { path: "organizer", select: "firstName lastName" },
        { path: "coordinator", select: "firstName lastName" },
        { path: "targetClasses", select: "name code" },
        { path: "targetSections", select: "name" },
      ]);

    res.status(200).json({
      success: true,
      count: events.length,
      data: events,
    });
  } catch (error) {
    console.error("Get Events By Type Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
