import { useEffect } from "react";
import { useStudent } from "@/context/student-context";
import {
  <PERSON>,
  <PERSON>r<PERSON><PERSON><PERSON>,
  PlusCircle,
  CalendarClock,
  GraduationCap,
} from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { StudentColumns } from "@/pages/dashboard/school-admin/students/student-columns";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";

const StudentDirectory = () => {
  const { students, isLoading, fetchAllStudents } = useStudent();

  useEffect(() => {
    fetchAllStudents();
  }, []);

  console.log(students);

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Student Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Students" },
          ]}
          actions={[
            {
              label: "New Student",
              icon: PlusCircle,
              href: "/dashboard/students/create",
            },
          ]}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Students"
            value={students.length}
            description="All registered students"
            icon={Users}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Active Students"
            value={students.filter((s) => s.status === "active").length}
            description="Currently enrolled"
            icon={UserCheck}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Recently Enrolled"
            value={
              students.filter((student) => {
                const createdAt = new Date(student.createdAt);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return createdAt >= thirtyDaysAgo;
              }).length
            }
            description="Enrolled in last 30 days"
            icon={CalendarClock}
            isLoading={isLoading}
          />

          <StatCard
            title="Top Class"
            value={(() => {
              if (students.length === 0) return "N/A";
              const classCount = students.reduce((acc, student) => {
                const className =
                  student.class?.name || student.class || "Unknown";
                acc[className] = (acc[className] || 0) + 1;
                return acc;
              }, {});
              let topClass = "Unknown";
              let max = 0;
              for (const [className, count] of Object.entries(classCount)) {
                if (count > max) {
                  topClass = className;
                  max = count;
                }
              }
              return topClass;
            })()}
            description="Most students in"
            icon={GraduationCap}
            isLoading={isLoading}
          />
        </div>

        <div>
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={students}
              columns={StudentColumns()}
              model="student"
            />
          )}
        </div>
      </main>
    </div>
  );
};

export default StudentDirectory;
