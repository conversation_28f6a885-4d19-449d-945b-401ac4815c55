import mongoose from "mongoose";

const EventSchema = new mongoose.Schema(
  {
    // Basic Event Information
    title: {
      type: String,
      required: [true, "Event title is required"],
      trim: true,
      maxlength: [100, "Title cannot exceed 100 characters"],
    },
    description: {
      type: String,
      required: [true, "Event description is required"],
      trim: true,
      maxlength: [500, "Description cannot exceed 500 characters"],
    },
    eventType: {
      type: String,
      required: [true, "Event type is required"],
      enum: [
        "academic",
        "sports",
        "cultural",
        "competition",
        "meeting",
        "celebration",
        "workshop",
        "seminar",
        "exam",
        "holiday",
        "other",
      ],
    },
    priority: {
      type: String,
      required: [true, "Priority is required"],
      enum: ["low", "medium", "high", "urgent"],
      default: "medium",
    },
    status: {
      type: String,
      required: [true, "Status is required"],
      enum: ["scheduled", "in_progress", "completed", "cancelled", "postponed"],
      default: "scheduled",
    },
    tags: {
      type: String,
      trim: true,
    },

    // Date and Time Information
    startDate: {
      type: Date,
      required: [true, "Start date is required"],
    },
    endDate: {
      type: Date,
      required: [true, "End date is required"],
    },
    startTime: {
      type: String,
      required: function () {
        return !this.isAllDayEvent;
      },
    },
    endTime: {
      type: String,
      required: function () {
        return !this.isAllDayEvent;
      },
    },
    isAllDayEvent: {
      type: Boolean,
      default: false,
    },

    // Location Information
    venueType: {
      type: String,
      required: [true, "Venue type is required"],
      enum: [
        "auditorium",
        "classroom",
        "sports_ground",
        "library",
        "laboratory",
        "conference_room",
        "online",
        "external",
        "other",
      ],
    },
    venueName: {
      type: String,
      required: [true, "Venue name is required"],
      trim: true,
    },
    venueAddress: {
      type: String,
      trim: true,
    },
    roomNumber: {
      type: String,
      trim: true,
    },
    capacity: {
      type: Number,
      min: [1, "Capacity must be at least 1"],
    },
    onlineLink: {
      type: String,
      trim: true,
    },

    // Attendee Information
    attendeeType: {
      type: String,
      required: [true, "Attendee type is required"],
      enum: [
        "all_students",
        "specific_classes",
        "teachers_only",
        "parents_only",
        "staff_only",
        "mixed_group",
      ],
    },
    targetClasses: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Class",
      },
    ],
    targetSections: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Section",
      },
    ],
    organizer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Teacher",
    },
    coordinator: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Teacher",
    },
    expectedAttendees: {
      type: Number,
      min: [1, "Expected attendees must be at least 1"],
    },

    // Additional Information
    requirements: {
      type: String,
      trim: true,
    },
    budget: {
      type: Number,
      min: [0, "Budget cannot be negative"],
    },
    instructions: {
      type: String,
      trim: true,
    },
    dresscode: {
      type: String,
      trim: true,
    },
    materialsNeeded: {
      type: String,
      trim: true,
    },

    // File Attachments
    eventBanner: {
      type: String,
      trim: true,
    },
    attachments: {
      type: String,
      trim: true,
    },

    // Registration Information
    registrationRequired: {
      type: Boolean,
      default: false,
    },
    registrationDeadline: {
      type: Date,
    },
    contactPerson: {
      type: String,
      trim: true,
    },
    contactPhone: {
      type: String,
      trim: true,
    },
    contactEmail: {
      type: String,
      trim: true,
      lowercase: true,
    },
    notes: {
      type: String,
      trim: true,
    },

    // System Information
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: [true, "Created by is required"],
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
EventSchema.index({ schoolId: 1 });
EventSchema.index({ startDate: 1 });
EventSchema.index({ endDate: 1 });
EventSchema.index({ eventType: 1 });
EventSchema.index({ status: 1 });
EventSchema.index({ priority: 1 });
EventSchema.index({ venueType: 1 });
EventSchema.index({ attendeeType: 1 });
EventSchema.index({ createdBy: 1 });
EventSchema.index({ isActive: 1 });

// Compound indexes
EventSchema.index({ schoolId: 1, startDate: 1 });
EventSchema.index({ schoolId: 1, eventType: 1 });
EventSchema.index({ schoolId: 1, status: 1 });

export default mongoose.model("Event", EventSchema);
