import { InfoCard } from "@/components/dashboard/info-card";
import { <PERSON>Field } from "@/components/dashboard/data-field";
import { FileText, Calendar, Info, Edit } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { ScrollArea } from "@/components/ui/scroll-area";
import { StatCard } from "@/components/dashboard/stat-card";
import { Container } from "@/components/ui/container";
import { PageHeader } from "@/components/dashboard/page-header";

export function TermDetails({ selectedTerm, isLoading }) {
  if (!selectedTerm) {
    return (
      <div className="flex flex-col items-center justify-center md:h-[calc(100vh-4rem)] space-y-4">
        <p className="text-muted-foreground">
          {isLoading ? "Loading terms..." : "No term selected"}
        </p>
        {!isLoading && (
          <Button asChild>
            <Link to="/dashboard/academics/terms/create">
              Create a new term
            </Link>
          </Button>
        )}
      </div>
    );
  }

  return (
    <>
      <header className="border-b bg-background">
        <Container className="py-4">
          <PageHeader
            title={`${selectedTerm.name} Details`}
            isLoading={isLoading}
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Academics", href: "/dashboard/academics" },
              {
                label: "Terms",
                href: "/dashboard/academics/terms",
              },
              { label: selectedTerm.code },
            ]}
            actions={[
              {
                label: "Edit Term",
                icon: Edit,
                href: `/dashboard/academics/terms/${selectedTerm._id}/edit`,
              },
            ]}
          />
        </Container>
      </header>
      <ScrollArea className="h-[calc(100vh-10rem)]">
        <main className="p-4 lg:p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6 mb-6">
            <StatCard
              title="Term Code"
              value={selectedTerm.code}
              description={
                selectedTerm.status === "active"
                  ? "Active Term"
                  : "Inactive Term"
              }
              icon={Calendar}
              loading={isLoading}
              valueClassName="uppercase"
              descriptionClassName="capitalize text-sm"
            />
            <StatCard
              title="Academic Year"
              value={selectedTerm.academicYear || "Not specified"}
              description={`Start: ${
                selectedTerm.startDate
                  ? new Date(selectedTerm.startDate).toLocaleDateString()
                  : "N/A"
              }`}
              icon={Calendar}
              loading={isLoading}
              valueClassName="text-2xl"
              descriptionClassName="capitalize"
            />
            <StatCard
              title="Status"
              value={selectedTerm.status}
              description={selectedTerm.isActive ? "Active" : "Inactive"}
              icon={Info}
              loading={isLoading}
              valueClassName="text-2xl font-bold"
              descriptionClassName="text-sm text-muted-foreground"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <InfoCard
              icon={Calendar}
              title="Term Information"
              isLoading={isLoading}
            >
              <div className="space-y-4">
                <DataField label="Term Name" value={selectedTerm.name} />
                <DataField
                  label="Term Code"
                  value={selectedTerm.code}
                  copyable
                />
                <DataField
                  label="Academic Year"
                  value={selectedTerm.academicYear}
                />
                <DataField
                  label="Start Date"
                  value={
                    selectedTerm.startDate
                      ? new Date(selectedTerm.startDate).toLocaleDateString()
                      : "N/A"
                  }
                />
                <DataField
                  label="End Date"
                  value={
                    selectedTerm.endDate
                      ? new Date(selectedTerm.endDate).toLocaleDateString()
                      : "N/A"
                  }
                />
              </div>
            </InfoCard>
            <InfoCard
              icon={FileText}
              title="Additional Information"
              isLoading={isLoading}
            >
              <div className="space-y-4">
                <DataField
                  label="Description"
                  value={selectedTerm.description || "No description"}
                />
                <DataField
                  label="Notes"
                  value={selectedTerm.notes || "No notes"}
                />
              </div>
            </InfoCard>
          </div>
        </main>
      </ScrollArea>
    </>
  );
}
