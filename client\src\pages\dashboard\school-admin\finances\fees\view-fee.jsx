import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useFee } from "@/context/fee-context";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/utils/date-filters";
import {
  DollarSign,
  ArrowLeft,
  Edit,
  Trash2,
  Calendar,
  CreditCard,
  FileText,
  Building,
  Settings,
  Clock,
  CheckCircle,
  XCircle,
  BookOpen,
  Users,
  AlertCircle,
  Percent,
  CalendarDays,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { InfoCard } from "@/components/dashboard/info-card";
import { DataField } from "@/components/dashboard/data-field";
import { ViewDetailSkeleton } from "@/components/dashboard/view-detail-skeleton";

const ViewFee = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchFeeById, removeFee, updateStatus } = useFee();
  const [fee, setFee] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [openDelete, setOpenDelete] = useState(false);
  const [openStatusChange, setOpenStatusChange] = useState(false);

  useEffect(() => {
    const fetchFee = async () => {
      try {
        setIsLoading(true);
        const data = await fetchFeeById(id);
        setFee(data);
      } catch (error) {
        console.error("Failed to fetch fee:", error);
        toast.error("Error", {
          description: error.message || "Failed to load fee details",
        });
        navigate("/dashboard/fees");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchFee();
    } else {
      console.error("No fee ID provided");
      setIsLoading(false);
    }
  }, [id]);

  const handleDelete = async () => {
    try {
      await removeFee(fee._id);
      toast.success("Fee deleted successfully", {
        description: `${fee.feeName} has been removed.`,
      });
      navigate("/dashboard/fees");
    } catch (error) {
      console.error("Failed to delete fee:", error);
      toast.error("Delete Failed", {
        description: error.message || "Failed to delete fee",
      });
    }
  };

  const handleStatusChange = async () => {
    try {
      const newStatus = fee.status === "active" ? "inactive" : "active";
      await updateStatus(fee._id, { status: newStatus });
      setOpenStatusChange(false);
      setFee((prevFee) => ({
        ...prevFee,
        status: newStatus,
      }));
      toast.success("Status updated successfully", {
        description: `${fee.feeName} status has been changed to ${newStatus}.`,
      });
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Update Failed", {
        description: error.message || "Failed to update fee status",
      });
    }
  };

  const formatCurrency = (amount) => {
    return amount ? `₹${amount.toLocaleString()}` : "Not specified";
  };

  const getDaysUntilDue = (dueDate) => {
    if (!dueDate) return null;
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return `${Math.abs(diffDays)} days overdue`;
    if (diffDays === 0) return "Due today";
    if (diffDays === 1) return "Due tomorrow";
    return `Due in ${diffDays} days`;
  };

  const getDueDateStatus = (dueDate) => {
    if (!dueDate) return "secondary";
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = due - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return "destructive";
    if (diffDays <= 7) return "warning";
    return "default";
  };

  if (isLoading) {
    return <ViewDetailSkeleton />;
  }

  return (
    <Container className="py-8">
      <div className="space-y-6">
        <PageHeader
          title="Fee Details"
          isLoading={isLoading}
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Finances", href: "/dashboard/finances" },
            { label: "Fees", href: "/dashboard/finances/fees" },
            { label: fee.feeName },
          ]}
          actions={[
            {
              label: "Edit Fee",
              icon: Edit,
              href: `/dashboard/finances/fees/${fee._id}/edit`,
            },
            {
              label: "Back to Fees",
              icon: ArrowLeft,
              href: "/dashboard/finances/fees",
              variant: "outline",
            },
          ]}
        />

        <Card className="border-0 bg-gradient-to-r from-primary/10 to-secondary/10 overflow-hidden relative ">
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full -mr-16 -mt-16"></div>
          <CardContent className="p-">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium mb-1">Total Fee Amount</p>
                <p className="text-3xl font-bold ">
                  {formatCurrency(fee.amount)}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm mb-1">Due Date</p>
                <p className="text-lg font-semibold ">
                  {formatDate(fee.dueDate)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-1">
            <div className="grid grid-cols-1 gap-6">
              <InfoCard
                icon={DollarSign}
                title="Fee Overview"
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="Fee Name"
                    value={fee.feeName}
                    icon={FileText}
                  />
                  <DataField label="Fee Code" value={fee.feeCode} copyable />
                  <DataField label="Fee Type" value={fee.feeType} />
                  <DataField
                    label="Amount"
                    value={formatCurrency(fee.amount)}
                  />
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                      Status
                    </label>
                    <Badge
                      variant={
                        fee.status === "active" ? "default" : "secondary"
                      }
                      className={`w-fit ${
                        fee.status === "active"
                          ? "bg-primary text-primary-foreground"
                          : "bg-secondary text-secondary-foreground"
                      }`}
                    >
                      {fee.status === "active" ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1" />
                      )}
                      {fee.status?.charAt(0).toUpperCase() +
                        fee.status?.slice(1)}
                    </Badge>
                  </div>
                </div>
              </InfoCard>

              <InfoCard
                icon={Calendar}
                title="Due Date Information"
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="Due Date"
                    value={fee.dueDate ? formatDate(fee.dueDate) : null}
                    icon={Calendar}
                  />
                  {fee.dueDate && (
                    <div className="space-y-1">
                      <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                        Due Status
                      </label>
                      <Badge
                        variant={getDueDateStatus(fee.dueDate)}
                        className="w-fit"
                      >
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {getDaysUntilDue(fee.dueDate)}
                      </Badge>
                    </div>
                  )}
                  {fee.lateFeeAmount && (
                    <DataField
                      label="Late Fee Amount"
                      value={formatCurrency(fee.lateFeeAmount)}
                      icon={Percent}
                    />
                  )}
                  {fee.lateFeeGracePeriod && (
                    <DataField
                      label="Grace Period"
                      value={`${fee.lateFeeGracePeriod} days`}
                      icon={CalendarDays}
                    />
                  )}
                </div>
              </InfoCard>

              <InfoCard
                icon={FileText}
                title="Description"
                isLoading={isLoading}
              >
                <div className="bg-muted/50 rounded-lg p-4 min-h-[120px]">
                  <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                    {fee.description || "No description available."}
                  </p>
                </div>
              </InfoCard>
            </div>
          </div>

          <div className="md:col-span-2 rounded-xl">
            <Tabs defaultValue="basic" className="w-full">
              <div className="mb-4">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">
                    <DollarSign className="h-4 w-4" />
                    <span className="hidden sm:inline">Basic Info</span>
                  </TabsTrigger>
                  <TabsTrigger value="academic">
                    <BookOpen className="h-4 w-4" />
                    <span className="hidden sm:inline">Academic</span>
                  </TabsTrigger>
                  <TabsTrigger value="payment">
                    <CreditCard className="h-4 w-4" />
                    <span className="hidden sm:inline">Payment</span>
                  </TabsTrigger>
                  <TabsTrigger value="system">
                    <Settings className="h-4 w-4" />
                    <span className="hidden sm:inline">System</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={DollarSign}
                      title="Fee Information"
                      isLoading={isLoading}
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <DataField
                          label="Fee Name"
                          value={fee.feeName}
                          icon={FileText}
                        />
                        <DataField
                          label="Fee Code"
                          value={fee.feeCode}
                          copyable
                        />
                        <DataField label="Fee Type" value={fee.feeType} />
                        <DataField
                          label="Amount"
                          value={formatCurrency(fee.amount)}
                          icon={DollarSign}
                        />
                      </div>
                    </InfoCard>
                  </div>

                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={FileText}
                      title="Description & Notes"
                      isLoading={isLoading}
                    >
                      <div className="space-y-4">
                        <div>
                          <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide block mb-2">
                            Description
                          </label>
                          <div className="bg-muted/50 rounded-lg p-4 min-h-[80px]">
                            <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                              {fee.description || "No description provided."}
                            </p>
                          </div>
                        </div>
                        {fee.notes && (
                          <div>
                            <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide block mb-2">
                              Additional Notes
                            </label>
                            <div className="bg-muted/50 rounded-lg p-4 min-h-[80px]">
                              <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                                {fee.notes}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </InfoCard>
                  </div>
                </div>
              </TabsContent>

              {/* Academic Information Tab */}
              <TabsContent value="academic" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InfoCard
                    icon={BookOpen}
                    title="Class Information"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Class"
                        value={fee.class?.name || fee.class}
                        icon={BookOpen}
                      />
                      {fee.class?.section && (
                        <DataField label="Section" value={fee.class.section} />
                      )}
                      {fee.class?.grade && (
                        <DataField label="Grade" value={fee.class.grade} />
                      )}
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={Calendar}
                    title="Term Information"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Term"
                        value={fee.term?.name || fee.term}
                        icon={Calendar}
                      />
                      {fee.term?.startDate && (
                        <DataField
                          label="Term Start"
                          value={formatDate(fee.term.startDate)}
                        />
                      )}
                      {fee.term?.endDate && (
                        <DataField
                          label="Term End"
                          value={formatDate(fee.term.endDate)}
                        />
                      )}
                    </div>
                  </InfoCard>

                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={Building}
                      title="School Information"
                      isLoading={isLoading}
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <DataField
                          label="School"
                          value={fee.schoolId?.name || fee.schoolId}
                          icon={Building}
                        />
                        {fee.schoolId?.code && (
                          <DataField
                            label="School Code"
                            value={fee.schoolId.code}
                          />
                        )}
                      </div>
                    </InfoCard>
                  </div>
                </div>
              </TabsContent>

              {/* Payment Information Tab */}
              <TabsContent value="payment" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InfoCard
                    icon={Calendar}
                    title="Due Date & Late Fees"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Due Date"
                        value={fee.dueDate ? formatDate(fee.dueDate) : null}
                        icon={Calendar}
                      />
                      {fee.dueDate && (
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                            Due Status
                          </label>
                          <Badge
                            variant={getDueDateStatus(fee.dueDate)}
                            className="w-fit"
                          >
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {getDaysUntilDue(fee.dueDate)}
                          </Badge>
                        </div>
                      )}
                      {fee.lateFeeAmount && (
                        <DataField
                          label="Late Fee Amount"
                          value={formatCurrency(fee.lateFeeAmount)}
                          icon={Percent}
                        />
                      )}
                      {fee.lateFeeGracePeriod && (
                        <DataField
                          label="Grace Period"
                          value={`${fee.lateFeeGracePeriod} days`}
                          icon={CalendarDays}
                        />
                      )}
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={CalendarDays}
                    title="Effective Period"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Effective Date"
                        value={
                          fee.effectiveDate
                            ? formatDate(fee.effectiveDate)
                            : null
                        }
                        icon={Calendar}
                      />
                      <DataField
                        label="Expiry Date"
                        value={
                          fee.expiryDate ? formatDate(fee.expiryDate) : null
                        }
                        icon={Calendar}
                      />
                      {fee.effectiveDate && fee.expiryDate && (
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                            Validity Status
                          </label>
                          <Badge
                            variant={
                              new Date() >= new Date(fee.effectiveDate) &&
                              new Date() <= new Date(fee.expiryDate)
                                ? "default"
                                : "secondary"
                            }
                          >
                            {new Date() >= new Date(fee.effectiveDate) &&
                            new Date() <= new Date(fee.expiryDate)
                              ? "Valid"
                              : "Invalid"}
                          </Badge>
                        </div>
                      )}
                    </div>
                  </InfoCard>

                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={Users}
                      title="Administrative Details"
                      isLoading={isLoading}
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <DataField
                          label="Created By"
                          value={fee.createdBy}
                          icon={Users}
                        />
                        <DataField
                          label="Approved By"
                          value={fee.approvedBy}
                          icon={Users}
                        />
                      </div>
                    </InfoCard>
                  </div>
                </div>
              </TabsContent>

              {/* System Information Tab */}
              <TabsContent value="system" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InfoCard
                    icon={Clock}
                    title="Timestamps"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Created At"
                        value={formatDate(fee.createdAt)}
                        icon={Clock}
                      />
                      <DataField
                        label="Last Updated"
                        value={formatDate(fee.updatedAt)}
                        icon={Clock}
                      />
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={Settings}
                    title="System Details"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField label="Fee ID" value={fee._id} copyable />
                      <DataField
                        label="Fee Code"
                        value={fee.feeCode}
                        copyable
                      />
                      <div className="space-y-1">
                        <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                          Status
                        </label>
                        <Badge
                          variant={
                            fee.status === "active" ? "default" : "secondary"
                          }
                          className={`w-fit ${
                            fee.status === "active"
                              ? "bg-primary text-primary-foreground"
                              : "bg-secondary text-secondary-foreground"
                          }`}
                        >
                          {fee.status === "active" ? (
                            <CheckCircle className="h-3 w-3 mr-1" />
                          ) : (
                            <XCircle className="h-3 w-3 mr-1" />
                          )}
                          {fee.status?.charAt(0).toUpperCase() +
                            fee.status?.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  </InfoCard>

                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={Building}
                      title="School Information"
                      isLoading={isLoading}
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <DataField
                          label="School ID"
                          value={fee.schoolId?._id || fee.schoolId}
                          copyable
                        />
                        <DataField
                          label="School Name"
                          value={fee.schoolId?.name || "Not populated"}
                        />
                      </div>
                    </InfoCard>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-destructive" />
              Delete {fee.feeName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the fee
              record and remove all associated data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive hover:bg-destructive/90"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Fee
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Status Change Confirmation Dialog */}
      <AlertDialog open={openStatusChange} onOpenChange={setOpenStatusChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              {fee.status === "active" ? (
                <XCircle className="h-5 w-5 text-muted-foreground" />
              ) : (
                <CheckCircle className="h-5 w-5 text-primary" />
              )}
              {fee.status === "active" ? "Deactivate" : "Activate"}{" "}
              {fee.feeName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to{" "}
              {fee.status === "active" ? "deactivate" : "activate"} this fee?
              {fee.status === "active"
                ? " This will prevent new payments for this fee."
                : " This will allow payments for this fee again."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleStatusChange}>
              {fee.status === "active" ? "Deactivate" : "Activate"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Container>
  );
};

export default ViewFee;
