import React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { PhoneInput } from "@/components/form-inputs/phone-input";
import { Building, User } from "lucide-react";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNavigate } from "react-router-dom";
import { booleanOptions, departmentTypes } from "@/utils/form-options";
import { useDepartment } from "@/context/department-context";

export function DepartmentForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { addDepartment, editDepartment } = useDepartment();

  const form = useForm({
    defaultValues: {
      // Basic Information
      name: initialData?.name || "",
      code: initialData?.code || "",
      abbreviation: initialData?.abbreviation || "",
      type: initialData?.type || "",
      establishedYear: initialData?.establishedYear || "",
      isActive: initialData?.isActive || "true",
      hasLab: initialData?.hasLab || "false",
      hasCourse: initialData?.hasCourse || "false",
      description: initialData?.description || "",

      // Department Head Information
      head: initialData?.head || "",
      headTitle: initialData?.headTitle || "",
      headEmail: initialData?.headEmail || "",
      headPhone: initialData?.headPhone || "",
    },
  });

  const handleSubmit = async (data) => {
    try {
      if (editingId) {
        await editDepartment(editingId, data);
        toast.success("Department updated successfully!", {
          description: `${data.name} has been updated.`,
        });
        navigate("/dashboard/academics/departments");
      } else {
        await addDepartment(data);
        toast.success("Department created successfully!", {
          description: `${data.name} has been added to the department list.`,
        });
      }
    } catch (error) {
      console.error("Department form submission error:", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to save department. Please try again."
      );
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Basic Information */}
          <FormCard title="Basic Information" icon={Building}>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <TextInput
                form={form}
                name="name"
                label="Department Name"
                placeholder="e.g., Computer Science"
                validation={{ required: "Department name is required" }}
              />
              <TextInput
                form={form}
                name="code"
                label="Department Code"
                placeholder="CS"
                validation={{
                  required: "Department code is required",
                  minLength: {
                    value: 2,
                    message: "Code must be at least 2 characters",
                  },
                  maxLength: {
                    value: 10,
                    message: "Code must not exceed 10 characters",
                  },
                }}
              />

              <div className="lg:col-span-2">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <TextInput
                    form={form}
                    name="abbreviation"
                    label="Abbreviation"
                    placeholder="CS Dept."
                    validation={{
                      required: "Abbreviation is required",
                      maxLength: {
                        value: 20,
                        message: "Abbreviation must not exceed 20 characters",
                      },
                    }}
                  />
                  <SelectInput
                    form={form}
                    name="type"
                    label="Department Type"
                    placeholder="Select type"
                    options={departmentTypes}
                    validation={{ required: "Department type is required" }}
                  />
                  <DateInput
                    form={form}
                    name="establishedYear"
                    label="Established Date"
                    placeholder="Select date"
                    validation={{ required: "Established date is required" }}
                  />
                </div>
              </div>

              <div className="lg:col-span-2">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div className="space-y-4">
                    <SelectInput
                      form={form}
                      name="isActive"
                      label="Is Active"
                      options={booleanOptions}
                      placeholder="Select option"
                      validation={{ required: "Active status is required" }}
                    />
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <SelectInput
                        form={form}
                        name="hasLab"
                        label="Has Lab"
                        options={booleanOptions}
                        placeholder="Select option"
                        validation={{ required: "Lab status is required" }}
                      />
                      <SelectInput
                        form={form}
                        name="hasCourse"
                        label="Has Courses"
                        options={booleanOptions}
                        placeholder="Select option"
                        validation={{ required: "Course status is required" }}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <TextareaInput
                      form={form}
                      name="description"
                      label="Description"
                      placeholder="Brief description of the department, its mission, and objectives..."
                      validation={{
                        required: "Description is required",
                        minLength: {
                          value: 10,
                          message: "Description must be at least 10 characters",
                        },
                        maxLength: {
                          value: 500,
                          message: "Description must not exceed 500 characters",
                        },
                      }}
                      inputProps={{ rows: 4 }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </FormCard>

          {/* Department Head Information */}
          <FormCard title="Department Head Information" icon={User}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <TextInput
                form={form}
                name="head"
                label="Head Name"
                placeholder="Dr. John Smith"
                validation={{
                  required: "Department head name is required",
                  minLength: {
                    value: 2,
                    message: "Name must be at least 2 characters",
                  },
                }}
              />
              <TextInput
                form={form}
                name="headTitle"
                label="Academic Title"
                placeholder="Professor & Department Head"
                validation={{ required: "Head title is required" }}
              />
              <TextInput
                form={form}
                name="headEmail"
                label="Head Email"
                type="email"
                placeholder="<EMAIL>"
                validation={{
                  required: "Head email is required",
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: "Please enter a valid email address",
                  },
                }}
              />
              <PhoneInput
                form={form}
                name="headPhone"
                label="Head Phone Number"
                placeholder="+****************"
                validation={{ required: "Head phone number is required" }}
              />
            </div>
          </FormCard>

          {/* Form Footer */}
          <FormFooter
            href="/dashboard/academics/departments"
            editingId={editingId}
            title="Department"
          />
        </form>
      </Form>
    </div>
  );
}
