import express from "express";
import {
  createSection,
  getAllSections,
  getSectionById,
  updateSection,
  deleteSection,
  updateSectionStatus,
  getSectionsByClass,
} from "../controllers/section.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createSection
);

router.get(
  "/",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getAllSections
);

router.get(
  "/class/:classId",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getSectionsByClass
);

router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getSectionById
);

router.put(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  updateSection
);

router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteSection
);

router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateSectionStatus
);

export default router;
