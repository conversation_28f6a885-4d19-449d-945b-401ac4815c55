import { InfoCard } from "@/components/dashboard/info-card";
import { DataField } from "@/components/dashboard/data-field";
import {
  FileText,
  ListFilter,
  Book,
  ClipboardList,
  Info,
  Edit,
  GraduationCap,
  Target,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { ScrollArea } from "@/components/ui/scroll-area";
import { StatCard } from "@/components/dashboard/stat-card";
import { Container } from "@/components/ui/container";
import { PageHeader } from "@/components/dashboard/page-header";

export function SubjectDetails({ selectedSubject, isLoading }) {
  if (!selectedSubject) {
    return (
      <div className="flex flex-col items-center justify-center md:h-[calc(100vh-4rem)] space-y-4">
        <p className="text-muted-foreground">
          {isLoading ? "Loading subjects..." : "No subject selected"}
        </p>
        {!isLoading && (
          <Button asChild>
            <Link to="/dashboard/academics/subjects/create">
              Create a new subject
            </Link>
          </Button>
        )}
      </div>
    );
  }

  return (
    <>
      <header className="border-b bg-background">
        <Container className="py-4">
          <PageHeader
            title={`${selectedSubject.name} Details`}
            isLoading={isLoading}
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Academics", href: "/dashboard/academics" },
              {
                label: "Subjects",
                href: "/dashboard/academics/subjects",
              },
              { label: selectedSubject.code },
            ]}
            actions={[
              {
                label: "Edit Subject",
                icon: Edit,
                href: `/dashboard/academics/subjects/${selectedSubject._id}/edit`,
              },
            ]}
          />
        </Container>
      </header>
      <ScrollArea className="h-[calc(100vh-10rem)]">
        <main className="p-4 lg:p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6 mb-6">
            <StatCard
              title="Subject Code"
              value={selectedSubject.code}
              description={
                selectedSubject.isElective ? "Elective Subject" : "Core Subject"
              }
              icon={Book}
              loading={isLoading}
              valueClassName="uppercase"
              descriptionClassName="capitalize text-sm"
            />

            <StatCard
              title="Credit Hours"
              value={selectedSubject.credit || "Not specified"}
              description={`Semester: ${
                selectedSubject.semester || "Not specified"
              }`}
              icon={GraduationCap}
              loading={isLoading}
              valueClassName="text-2xl"
              descriptionClassName="capitalize"
            />

            <StatCard
              title="Pass Percentage"
              value={
                selectedSubject.totalMarks && selectedSubject.passMarks
                  ? `${Math.round(
                      (selectedSubject.passMarks / selectedSubject.totalMarks) *
                        100
                    )}%`
                  : "Not Set"
              }
              description={`Total Marks: ${selectedSubject.totalMarks}`}
              icon={Target}
              loading={isLoading}
              valueClassName="text-2xl font-bold"
              descriptionClassName="text-sm text-muted-foreground"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <InfoCard
              icon={Book}
              title="Subject Information"
              isLoading={isLoading}
            >
              <div className="space-y-4">
                <DataField label="Subject Name" value={selectedSubject.name} />
                <DataField
                  label="Subject Code"
                  value={selectedSubject.code}
                  copyable
                />
                <DataField
                  label="Subject Type"
                  value={selectedSubject.subjectType}
                  className="capitalize"
                />
                <DataField
                  label="Department"
                  value={selectedSubject.department?.name}
                />
              </div>
            </InfoCard>

            <InfoCard
              icon={Target}
              title="Assessment Details"
              isLoading={isLoading}
            >
              <div className="space-y-4">
                <DataField
                  label="Total Marks"
                  value={selectedSubject.totalMarks || "Not specified"}
                />
                <DataField
                  label="Pass Marks"
                  value={selectedSubject.passMarks || "Not specified"}
                />
                <DataField
                  label="Pass Percentage"
                  value={
                    selectedSubject.totalMarks && selectedSubject.passMarks
                      ? `${Math.round(
                          (selectedSubject.passMarks /
                            selectedSubject.totalMarks) *
                            100
                        )}%`
                      : "Not calculated"
                  }
                />
                <DataField
                  label="Is Elective"
                  value={selectedSubject.isElective ? "Yes" : "No"}
                  badge={{
                    variant: selectedSubject.isElective
                      ? "default"
                      : "secondary",
                  }}
                />
              </div>
            </InfoCard>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <InfoCard
              icon={ClipboardList}
              title="Academic Properties"
              isLoading={isLoading}
            >
              <div className="space-y-4">
                <DataField
                  label="Credit Hours"
                  value={selectedSubject.credit || "Not specified"}
                />
                <DataField
                  label="Semester"
                  value={selectedSubject.semester || "Not specified"}
                />
                <DataField
                  label="Current Status"
                  value={selectedSubject.status}
                  className="capitalize"
                  badge={{
                    variant:
                      selectedSubject.status === "active"
                        ? "default"
                        : selectedSubject.status === "draft"
                        ? "outline"
                        : "secondary",
                  }}
                />
              </div>
            </InfoCard>

            {selectedSubject.prerequisites && (
              <InfoCard
                icon={FileText}
                title="Prerequisites"
                isLoading={isLoading}
              >
                <p className="text-muted-foreground leading-relaxed">
                  {selectedSubject.prerequisites}
                </p>
              </InfoCard>
            )}
          </div>

          {!isLoading && selectedSubject.description && (
            <InfoCard
              icon={Info}
              title="Description"
              className="mt-6"
              isLoading={isLoading}
            >
              <p className="text-muted-foreground leading-relaxed">
                {selectedSubject.description}
              </p>
            </InfoCard>
          )}
        </main>
      </ScrollArea>
    </>
  );
}
