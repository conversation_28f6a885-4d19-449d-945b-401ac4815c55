import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import {
  DollarSign,
  Calendar,
  BookOpen,
  FileText,
  Settings,
  CreditCard,
} from "lucide-react";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNavigate } from "react-router-dom";
import { useClass } from "@/context/class-context";
import { useTerm } from "@/context/term-context";
import { feeTypeOptions } from "@/utils/form-options";
import { useFee } from "@/context/fee-context";

const statusOptions = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
];

export function FeeForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { fetchAllClasses, classOptions } = useClass();
  const { fetchAllTerms, termOptions } = useTerm();
  const { addFee, editFee } = useFee();

  const form = useForm({
    defaultValues: {
      // Basic Fee Information
      feeType: initialData?.feeType || "",
      feeName: initialData?.feeName || "",
      feeCode: initialData?.feeCode || "",
      amount: initialData?.amount || "",
      description: initialData?.description || "",

      // Academic Information
      class: initialData?.class || "",
      term: initialData?.term || "",

      // Payment Information
      dueDate: initialData?.dueDate || "",
      lateFeeAmount: initialData?.lateFeeAmount || "",
      lateFeeGracePeriod: initialData?.lateFeeGracePeriod || "",
      status: initialData?.status || "",

      // Administrative
      createdBy: initialData?.createdBy || "",
      approvedBy: initialData?.approvedBy || "",
      effectiveDate: initialData?.effectiveDate || "",
      expiryDate: initialData?.expiryDate || "",
      notes: initialData?.notes || "",
    },
  });

  useEffect(() => {
    fetchAllClasses();
    fetchAllTerms();
  }, []);

  const onSubmit = async (data) => {
    try {
      if (editingId) {
        await editFee(editingId, data);
        toast.success("Fee information has been updated successfully.", {
          description: `${data.feeName} has been updated.`,
        });
        navigate("/dashboard/fees");
      } else {
        await addFee(data);
        toast.success("New fee has been created successfully.", {
          description: `${data.feeName} has been added to the fee list.`,
        });
      }
    } catch (error) {
      console.error("Error submitting fee form:", error);
      toast.error("Error", {
        description:
          error.message || "Failed to save fee information. Please try again.",
      });
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <div className="space-y-6">
              <FormCard title="Academic Information" icon={BookOpen}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ComboboxInput
                    form={form}
                    name="class"
                    label="Class"
                    placeholder="Select class"
                    options={classOptions}
                    href="/dashboard/academics/classes"
                    toolTipText="Create a new class"
                  />
                  <ComboboxInput
                    form={form}
                    name="term"
                    label="Term"
                    placeholder="Select Term"
                    options={termOptions}
                    href="/dashboard/academics/terms"
                    toolTipText="Create a new term"
                  />
                </div>
              </FormCard>

              <FormCard title="Basic Fee Information" icon={DollarSign}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SelectInput
                    form={form}
                    name="feeType"
                    label="Fee Type"
                    placeholder="Select fee type"
                    options={feeTypeOptions}
                    validation={{ required: "Fee type is required" }}
                  />
                  <TextInput
                    form={form}
                    name="feeName"
                    label="Fee Name"
                    placeholder="Enter fee name"
                    validation={{ required: "Fee name is required" }}
                  />
                  <TextInput
                    form={form}
                    name="feeCode"
                    label="Fee Code"
                    placeholder="Enter unique fee code"
                    validation={{ required: "Fee code is required" }}
                  />
                  <TextInput
                    form={form}
                    name="amount"
                    label="Amount (₹)"
                    type="number"
                    placeholder="Enter amount"
                    validation={{
                      required: "Amount is required",
                      min: { value: 0, message: "Amount must be positive" },
                    }}
                  />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="description"
                      label="Description"
                      placeholder="Enter fee description"
                      inputProps={{ rows: 2 }}
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Payment Information" icon={CreditCard}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <DateInput
                    form={form}
                    name="dueDate"
                    label="Due Date"
                    placeholder="Select due date"
                    validation={{ required: "Due date is required" }}
                  />
                  <TextInput
                    form={form}
                    name="lateFeeAmount"
                    label="Late Fee Amount (₹)"
                    type="number"
                    placeholder="Enter late fee amount"
                  />
                  <TextInput
                    form={form}
                    name="lateFeeGracePeriod"
                    label="Late Fee Grace Period (days)"
                    type="number"
                    placeholder="Enter grace period in days"
                  />
                  <SelectInput
                    form={form}
                    name="status"
                    label="Status"
                    placeholder="Select status"
                    options={statusOptions}
                    validation={{ required: "Status is required" }}
                  />
                </div>
              </FormCard>

              <FormCard title="Administrative Information" icon={FileText}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <DateInput
                    form={form}
                    name="effectiveDate"
                    label="Effective Date"
                    placeholder="Select effective date"
                  />
                  <DateInput
                    form={form}
                    name="expiryDate"
                    label="Expiry Date"
                    placeholder="Select expiry date"
                  />
                  <TextInput
                    form={form}
                    name="createdBy"
                    label="Created By"
                    placeholder="Enter creator name"
                  />
                  <TextInput
                    form={form}
                    name="approvedBy"
                    label="Approved By"
                    placeholder="Enter approver name"
                  />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="notes"
                      label="Additional Notes"
                      placeholder="Enter any additional notes or comments"
                      inputProps={{ rows: 3 }}
                    />
                  </div>
                </div>
              </FormCard>
            </div>
          </div>
          <FormFooter
            href="finances/fees"
            parent=""
            title="Fee"
            editingId={editingId}
          />
        </form>
      </Form>
    </div>
  );
}
