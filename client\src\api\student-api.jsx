import axiosInstance from "./axios-instance";

export const createStudent = async (studentData) => {
  try {
    const response = await axiosInstance.post("/students/create", studentData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating student:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create student");
  }
};

export const getStudents = async () => {
  try {
    const response = await axiosInstance.get("/students");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching students:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch students");
  }
};

export const getStudentById = async (id) => {
  try {
    const response = await axiosInstance.get(`/students/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching student with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch student with ID ${id}`)
    );
  }
};

export const updateStudent = async (id, studentData) => {
  try {
    const response = await axiosInstance.put(`/students/${id}`, studentData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating student with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update student with ID ${id}`)
    );
  }
};

export const deleteStudent = async (id) => {
  try {
    const response = await axiosInstance.delete(`/students/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting student with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to delete student with ID ${id}`)
    );
  }
};

export const updateStudentStatus = async (id, status) => {
  try {
    const response = await axiosInstance.patch(`/students/${id}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating student status for student with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update student status for student with ID ${id}`)
    );
  }
};
