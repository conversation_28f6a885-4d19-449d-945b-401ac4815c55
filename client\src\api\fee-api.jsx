import axiosInstance from "./axios-instance";

export const createFee = async (feeData) => {
  try {
    const response = await axiosInstance.post("/fees/create", feeData);
    return response.data;
  } catch (error) {
    console.error("Error creating fee:", error.response?.data || error.message);
    throw error.response?.data || new Error("Failed to create fee");
  }
};

export const getFees = async (filters = {}) => {
  try {
    const queryParams = new URLSearchParams();
    const queryString = queryParams.toString();
    const url = queryString ? `/fees?${queryString}` : "/fees";
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching fees:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch fees");
  }
};

export const getFeeById = async (id) => {
  try {
    const response = await axiosInstance.get(`/fees/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching fee with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch fee with ID ${id}`)
    );
  }
};

export const updateFee = async (id, feeData) => {
  try {
    const response = await axiosInstance.put(`/fees/${id}`, feeData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating fee with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to update fee with ID ${id}`)
    );
  }
};

export const deleteFee = async (id) => {
  try {
    const response = await axiosInstance.delete(`/fees/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting fee with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to delete fee with ID ${id}`)
    );
  }
};

export const updateFeeStatus = async (id, status) => {
  try {
    const response = await axiosInstance.patch(`/fees/${id}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating fee status for fee with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update fee status for fee with ID ${id}`)
    );
  }
};

export const getFeesByClass = async (classId, filters = {}) => {
  try {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        queryParams.append(key, value);
      }
    });
    const queryString = queryParams.toString();
    const url = queryString
      ? `/fees/class/${classId}?${queryString}`
      : `/fees/class/${classId}`;
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching fees for class ${classId}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch fees for class ${classId}`)
    );
  }
};

export const getFeesByTerm = async (termId, filters = {}) => {
  try {
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        queryParams.append(key, value);
      }
    });
    const queryString = queryParams.toString();
    const url = queryString
      ? `/fees/term/${termId}?${queryString}`
      : `/fees/term/${termId}`;
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching fees for term ${termId}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch fees for term ${termId}`)
    );
  }
};
