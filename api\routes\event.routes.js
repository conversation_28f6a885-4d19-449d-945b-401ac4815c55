import express from "express";
import {
  createEvent,
  getAllEvents,
  getEventById,
  updateEvent,
  deleteEvent,
  updateEventStatus,
  updateEventActiveStatus,
  getEventsByDateRange,
  getUpcomingEvents,
  getEventsByType,
} from "../controllers/event.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

// Create new event
router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin", "teacher"]),
  createEvent
);

// Get all events with filters and pagination
router.get(
  "/",
  protect,
  authorize(["admin", "school-admin", "teacher", "student", "parent"]),
  getAllEvents
);

// Get upcoming events
router.get(
  "/upcoming",
  protect,
  authorize(["admin", "school-admin", "teacher", "student", "parent"]),
  getUpcomingEvents
);

// Get events by date range
router.get(
  "/date-range/:startDate/:endDate",
  protect,
  authorize(["admin", "school-admin", "teacher", "student", "parent"]),
  getEventsByDateRange
);

// Get events by type
router.get(
  "/type/:eventType",
  protect,
  authorize(["admin", "school-admin", "teacher", "student", "parent"]),
  getEventsByType
);

// Get event by ID
router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher", "student", "parent"]),
  getEventById
);

// Update event
router.put(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher"]),
  updateEvent
);

// Update event status (scheduled, in_progress, completed, cancelled, postponed)
router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin", "teacher"]),
  updateEventStatus
);

// Update event active status (active/inactive)
router.patch(
  "/:id/active-status",
  protect,
  authorize(["admin", "school-admin"]),
  updateEventActiveStatus
);

// Delete event
router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteEvent
);

export default router;
