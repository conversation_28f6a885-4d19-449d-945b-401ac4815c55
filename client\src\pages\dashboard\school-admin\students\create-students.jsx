import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { StudentForm } from "@/components/forms/dashboard/students/student-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useStudent } from "@/context/student-context";
import { toast } from "sonner";

const CreateStudents = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchStudentById, isLoading } = useStudent();
  const [studentData, setStudentData] = useState(null);
  const [loading, setLoading] = useState(!!id);

  useEffect(() => {
    const loadStudentData = async () => {
      if (id) {
        try {
          const data = await fetchStudentById(id);
          setStudentData(data);
        } catch (error) {
          console.error("Failed to fetch student data:", error);
          toast.error("Failed to load student data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadStudentData();
  }, [id, fetchStudentById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Student" : "Create New Student"}
        actions={[
          {
            label: "Back to Students",
            href: "/dashboard/students",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Students", href: "/dashboard/students" },
          { label: id ? "Edit Student" : "Create Student" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <StudentForm editingId={id} initialData={studentData} />
      )}
    </Container>
  );
};

export default CreateStudents;
