import axiosInstance from "./axios-instance";

export const createClass = async (classData) => {
  try {
    const response = await axiosInstance.post("/classes/create", classData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating class:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create class");
  }
};

export const getClasses = async (filters = {}) => {
  try {
    // Convert filters object to query string
    const queryParams = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        queryParams.append(key, value);
      }
    });

    const queryString = queryParams.toString();
    const url = queryString ? `/classes?${queryString}` : "/classes";

    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching classes:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch classes");
  }
};

export const getClassById = async (id) => {
  try {
    const response = await axiosInstance.get(`/classes/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching class with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch class with ID ${id}`)
    );
  }
};

export const updateClass = async (id, classData) => {
  try {
    const response = await axiosInstance.put(`/classes/${id}`, classData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating class with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to update class with ID ${id}`)
    );
  }
};

export const deleteClass = async (id) => {
  try {
    const response = await axiosInstance.delete(`/classes/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting class with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to delete class with ID ${id}`)
    );
  }
};

export const updateClassStatus = async (id, isActive) => {
  try {
    const response = await axiosInstance.patch(`/classes/${id}/status`, {
      isActive,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating class status for class with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update class status for class with ID ${id}`)
    );
  }
};

export const getClassesByDepartment = async (departmentId) => {
  try {
    const response = await axiosInstance.get(
      `/classes/department/${departmentId}`
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching classes for department ${departmentId}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch classes for department ${departmentId}`)
    );
  }
};

export const getClassesByAcademicYear = async (academicYear) => {
  try {
    const response = await axiosInstance.get(
      `/classes/academic-year/${academicYear}`
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching classes for academic year ${academicYear}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch classes for academic year ${academicYear}`)
    );
  }
};
