import React, { useState } from "react";
import { StatusColumn } from "@/components/data-table/data-table-columns/status-column";
import { ImageColumn } from "@/components/data-table/data-table-columns/image-column";
import { DateColumn } from "@/components/data-table/data-table-columns/data-columns";
import { SortableColumn } from "@/components/data-table/data-table-columns/sortable-column";
import { ActionColumn } from "@/components/data-table/data-table-columns/action-column";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { useTeacher } from "@/context/teacher-context";

export const TeacherColumns = () => {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <SortableColumn column={column} title="Teacher Name" />
      ),
      cell: ({ row }) => (
        <div className="flex items-center space-x-3">
          <ImageColumn
            src={row.original.profilePhoto}
            alt={`${row.original.firstName} ${row.original.lastName}`}
            fallbackText={`${row.original.firstName?.charAt(0) || ""}$
              {row.original.lastName?.charAt(0) || ""}`}
          />
          <div className="flex flex-col">
            <div className="font-medium">
              {row.original.title} {row.original.firstName}{" "}
              {row.original.lastName}
            </div>
            <div className="text-sm text-muted-foreground">
              {row.original.email}
            </div>
          </div>
        </div>
      ),
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "phone",
      header: ({ column }) => <SortableColumn column={column} title="Phone" />,
      cell: ({ row }) => (
        <div className="text-sm text-muted-foreground">
          {row.original.phone}
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "gender",
      header: ({ column }) => <SortableColumn column={column} title="Gender" />,
      cell: ({ row }) => (
        <div className="text-sm text-center">{row.original.gender}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "qualification",
      header: ({ column }) => (
        <SortableColumn column={column} title="Qualification" />
      ),
      cell: ({ row }) => (
        <div className="text-sm text-center">{row.original.qualification}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "designation",
      header: ({ column }) => (
        <SortableColumn column={column} title="Designation" />
      ),
      cell: ({ row }) => (
        <div className="text-sm text-center">{row.original.designation}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <StatusColumn
          row={row}
          statusField="status"
          variant={{
            active: "success",
            inactive: "destructive",
            "on-leave": "warning",
          }}
        />
      ),
      enableSorting: true,
    },
    {
      accessorKey: "createdAt",
      header: "Date Added",
      cell: ({ row }) => <DateColumn row={row} accessorKey="createdAt" />,
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      id: "actions",
      cell: ({ row }) => <TeacherActions row={row} />,
    },
  ];
};

const TeacherActions = ({ row }) => {
  const navigate = useNavigate();
  const { removeTeacher } = useTeacher();
  const [open, setOpen] = useState(false);

  const handleView = () => {
    navigate(`/dashboard/teachers/${row.original._id}`);
  };

  const handleEdit = () => {
    navigate(`/dashboard/teachers/${row.original._id}/edit`);
  };

  const handleDelete = async () => {
    try {
      await removeTeacher(row.original._id);
      toast.success(
        `${row.original.firstName} ${row.original.lastName} deleted successfully.`
      );
      setOpen(false);
    } catch (error) {
      console.error("Failed to delete teacher:", error);
      toast.error("Failed to delete teacher. Please try again.");
    }
  };

  return (
    <>
      <ActionColumn
        row={row}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={() => setOpen(true)}
      />
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogTrigger asChild />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Delete {row.original.firstName} {row.original.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this teacher? This action cannot
              be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
