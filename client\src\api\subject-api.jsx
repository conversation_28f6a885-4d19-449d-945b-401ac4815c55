import axiosInstance from "./axios-instance";

export const createSubject = async (subjectData) => {
  try {
    const response = await axiosInstance.post("/subjects/create", subjectData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating subject:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create subject");
  }
};

export const getSubjects = async () => {
  try {
    // Convert filters object to query string
    const queryParams = new URLSearchParams();
    const queryString = queryParams.toString();
    const url = queryString ? `/subjects?${queryString}` : "/subjects";

    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching subjects:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch subjects");
  }
};

export const getSubjectById = async (id) => {
  try {
    const response = await axiosInstance.get(`/subjects/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching subject with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch subject with ID ${id}`)
    );
  }
};

export const updateSubject = async (id, subjectData) => {
  try {
    const response = await axiosInstance.put(`/subjects/${id}`, subjectData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating subject with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update subject with ID ${id}`)
    );
  }
};

export const deleteSubject = async (id) => {
  try {
    const response = await axiosInstance.delete(`/subjects/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting subject with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to delete subject with ID ${id}`)
    );
  }
};

export const updateSubjectStatus = async (id, isActive) => {
  try {
    const response = await axiosInstance.patch(`/subjects/${id}/status`, {
      isActive,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating subject status for subject with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update subject status for subject with ID ${id}`)
    );
  }
};

export const getSubjectsByDepartment = async (departmentId) => {
  try {
    const response = await axiosInstance.get(
      `/subjects/department/${departmentId}`
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching subjects for department ${departmentId}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch subjects for department ${departmentId}`)
    );
  }
};
