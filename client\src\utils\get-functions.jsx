import {
  AlertCircle,
  AlertTriangle,
  Bell,
  Calendar,
  CheckCircle,
  Clock,
  Clock3,
  Edit,
  Info,
  PlayCircle,
  XCircle,
} from "lucide-react";

// Get icon based on notification type
export function getNotificationIcon(type) {
  switch (type) {
    case "Emergency":
      return <AlertTriangle className="h-6 w-6 text-destructive" />;
    case "Event":
      return <Calendar className="h-6 w-6 text-primary" />;
    case "Academic":
      return <Clock className="h-6 w-6 text-warning" />;
    default:
      return <Bell className="h-6 w-6 text-primary" />;
  }
}

// Get icon based on notification type
export function getStatusIcon(status) {
  switch (status) {
    case "sent":
      return <CheckCircle className="h-5 w-5 text-success" />;
    case "failed":
      return <XCircle className="h-5 w-5 text-destructive" />;
    case "scheduled":
      return <Clock3 className="h-5 w-5 text-warning" />;
    case "draft":
      return <Edit className="h-5 w-5 text-muted-foreground" />;
    case "in_progress":
      return <PlayCircle className="h-5 w-5 text-info" />;
    case "completed":
      return <CheckCircle className="h-5 w-5 text-success" />;
    case "cancelled":
      return <XCircle className="h-5 w-5 text-destructive" />;
    default:
      return <Info className="h-5 w-5 text-primary" />;
  }
}

// Get badge variant based on priority
export function getPriorityBadgeVariant(priority) {
  switch (priority?.toLowerCase()) {
    case "high":
      return "destructive";
    case "medium":
      return "default";
    case "low":
      return "secondary";
    default:
      return "outline";
  }
}

// Get badge variant based on status
export function getStatusBadgeVariant(status) {
  switch (status?.toLowerCase()) {
    case "sent":
      return "default";
    case "scheduled":
      return "secondary";
    case "draft":
      return "outline";
    case "failed":
      return "destructive";
    default:
      return "outline";
  }
}

// Get icon based on event type
export function getEventTypeIcon(eventType) {
  const typeIcons = {
    academic: "📚",
    sports: "⚽",
    cultural: "🎭",
    competition: "🏆",
    meeting: "👥",
    celebration: "🎉",
    workshop: "🔧",
    seminar: "💬",
    exam: "📝",
    holiday: "🏖️",
    other: "📅",
  };
  return typeIcons[eventType] || "📅";
}

// Get color based on event type
export function getEventTypeColor(type) {
  const colors = {
    exam: "bg-red-50 text-red-700 border-red-200",
    meeting: "bg-blue-50 text-blue-700 border-blue-200",
    workshop: "bg-green-50 text-green-700 border-green-200",
    conference: "bg-purple-50 text-purple-700 border-purple-200",
    seminar: "bg-orange-50 text-orange-700 border-orange-200",
    sports: "bg-yellow-50 text-yellow-700 border-yellow-200",
    cultural: "bg-pink-50 text-pink-700 border-pink-200",
    academic: "bg-indigo-50 text-indigo-700 border-indigo-200",
  };
  return colors[type] || "bg-gray-50 text-gray-700 border-gray-200";
}

// Get color based on status
export function getStatusColor(status) {
  const statusColors = {
    scheduled: "bg-yellow-100 text-yellow-800",
    in_progress: "bg-blue-100 text-blue-800",
    completed: "bg-green-100 text-green-800",
    cancelled: "bg-red-100 text-red-800",
    postponed: "bg-gray-100 text-gray-800",
  };
  return statusColors[status] || "bg-gray-100 text-gray-800";
}

// Calendar utility functions
export function getDaysInMonth(date) {
  const year = date.getFullYear();
  const month = date.getMonth();
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const daysInMonth = lastDay.getDate();
  const startingDayOfWeek = firstDay.getDay();

  const days = [];
  for (let i = 0; i < startingDayOfWeek; i++) {
    days.push(null);
  }
  for (let day = 1; day <= daysInMonth; day++) {
    days.push(new Date(year, month, day));
  }
  return days;
}

export function getDaysInWeek(date) {
  const currentDay = new Date(date);
  const day = currentDay.getDay();
  const startDate = new Date(currentDay);
  startDate.setDate(currentDay.getDate() - day);

  const days = [];
  for (let i = 0; i < 7; i++) {
    const weekDay = new Date(startDate);
    weekDay.setDate(startDate.getDate() + i);
    days.push(weekDay);
  }
  return days;
}

export function getTimeSlots() {
  const slots = [];
  for (let hour = 7; hour <= 19; hour++) {
    slots.push(hour);
  }
  return slots;
}

export function isToday(date) {
  if (!date) return false;
  const today = new Date();
  return date.toDateString() === today.toDateString();
}

export function isSelected(date, selectedDate) {
  if (!date) return false;
  return date.toDateString() === selectedDate.toDateString();
}

export function getEventsForDate(events, date) {
  if (!date) return [];
  return events.filter((event) => {
    const eventDate = new Date(event.startDate);
    return eventDate.toDateString() === date.toDateString();
  });
}

export function getEventsForHour(events, date, hour) {
  if (!date) return [];

  return events.filter((event) => {
    const eventDate = new Date(event.startDate);
    if (eventDate.toDateString() !== date.toDateString()) return false;

    if (event.isAllDayEvent) return true;

    const eventHour = parseInt(event.startTime?.split(":")[0] || 0);
    const eventEndHour = parseInt(event.endTime?.split(":")[0] || 0);

    return (
      eventHour === hour ||
      (eventHour < hour && eventEndHour > hour) ||
      (eventHour < hour && !event.endTime)
    );
  });
}

export function getEventStats(events) {
  const stats = {
    total: events.length,
    thisMonth: 0,
    classes: 0,
    meetings: 0,
    exams: 0,
  };

  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();

  events.forEach((event) => {
    const eventDate = new Date(event.startDate);

    // Count events in current month
    if (
      eventDate.getMonth() === currentMonth &&
      eventDate.getFullYear() === currentYear
    ) {
      stats.thisMonth++;
    }

    // Count by type
    switch (event.eventType) {
      case "academic":
        stats.classes++;
        break;
      case "meeting":
        stats.meetings++;
        break;
      case "exam":
        stats.exams++;
        break;
    }
  });

  return stats;
}

export function getUpcomingEvents(events, limit = 5) {
  const today = new Date();
  const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

  return events
    .filter((event) => {
      const eventDate = new Date(event.startDate);
      return eventDate >= today && eventDate <= nextWeek;
    })
    .sort((a, b) => new Date(a.startDate) - new Date(b.startDate))
    .slice(0, limit);
}
