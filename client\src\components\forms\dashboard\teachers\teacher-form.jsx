import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { PhoneInput } from "@/components/form-inputs/phone-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import {
  User,
  Briefcase,
  MapPin,
  BookOpen,
  FileText,
  Key,
  Phone,
} from "lucide-react";
import {
  genderOptions,
  bloodGroups,
  maritalStatuses,
  qualifications,
  employmentTypes,
  teachingExperience,
  titles,
} from "@/utils/form-options";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNavigate } from "react-router-dom";
import { PasswordInput } from "@/components/form-inputs/password-input";
import { FileInput } from "@/components/form-inputs/file-input";
import { useDepartment } from "@/context/department-context";
import { useSubject } from "@/context/subject-context";
import { useTeacher } from "@/context/teacher-context";

export function TeacherForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { fetchAllDepartments, departmentOptions } = useDepartment();
  const { fetchAllSubjects, subjectOptions } = useSubject();
  const { addTeacher, editTeacher } = useTeacher();

  const form = useForm({
    defaultValues: {
      // Personal Information
      title: initialData?.title || "",
      firstName: initialData?.firstName || "",
      lastName: initialData?.lastName || "",
      dateOfBirth: initialData?.dateOfBirth || "",
      gender: initialData?.gender || "",
      bloodGroup: initialData?.bloodGroup || "",
      maritalStatus: initialData?.maritalStatus || "",
      nationality: initialData?.nationality || "Indian",
      religion: initialData?.religion || "",
      employeeId: initialData?.employeeId || "",
      aadharNumber: initialData?.aadharNumber || "",
      panNumber: initialData?.panNumber || "",

      // Address Information
      address: initialData?.address || "",
      city: initialData?.city || "",
      state: initialData?.state || "",
      pincode: initialData?.pincode || "",
      country: initialData?.country || "India",

      // Contact Information
      personalEmail: initialData?.personalEmail || "",
      phone: initialData?.phone || "",
      alternatePhone: initialData?.alternatePhone || "",
      preferredContactMethod: initialData?.preferredContactMethod || "email",

      // Professional Information
      designation: initialData?.designation || "",
      department: initialData?.department || "",
      primarySubject: initialData?.primarySubject || "",
      secondarySubject: initialData?.secondarySubject || "",
      qualification: initialData?.qualification || "",
      employmentType: initialData?.employmentType || "",
      experience: initialData?.experience || "",
      previousExperience: initialData?.previousExperience || "",

      // Emergency Contact
      emergencyContactName: initialData?.emergencyContactName || "",
      emergencyContactPhone: initialData?.emergencyContactPhone || "",
      emergencyContactRelation: initialData?.emergencyContactRelation || "",

      // System Access & Permissions
      email: initialData?.email || "",
      password: initialData?.password || "",
      isActive: initialData?.isActive || true,

      // Bank Details
      bankName: initialData?.bankName || "",
      bankAccountNumber: initialData?.bankAccountNumber || "",
      ifscCode: initialData?.ifscCode || "",
      notes: initialData?.notes || "",

      // Additional Information
      specializations: initialData?.specializations || "",
      achievements: initialData?.achievements || "",
      additionalNotes: initialData?.additionalNotes || "",

      // File uploads
      profilePhoto: initialData?.profilePhoto || null,
    },
  });

  useEffect(() => {
    fetchAllDepartments();
    fetchAllSubjects();
  }, []);

  const onSubmit = async (data) => {
    try {
      if (editingId) {
        await editTeacher(editingId, data);
        toast.success("Teacher information has been updated successfully.", {
          description: `${data.name} has been updated.`,
        });
        navigate("/dashboard/teachers");
      } else {
        await addTeacher(data);
        toast.success("New teacher has been created successfully.", {
          description: `${data.name} has been added to the teacher list.`,
        });
      }
    } catch (error) {
      console.error("Error submitting teacher form:", error);
      toast.error({
        title: "Error",
        description:
          error.message ||
          "Failed to save teacher information. Please try again.",
      });
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <div className="space-y-6">
              <FormCard title="Personal Information" icon={User}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="title"
                        label="Title"
                        placeholder="Select title"
                        options={titles}
                        validation={{ required: "Title is required" }}
                      />
                      <div className="md:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <TextInput
                          form={form}
                          name="firstName"
                          label="First Name"
                          placeholder="Enter first name"
                          validation={{ required: "First name is required" }}
                        />
                        <TextInput
                          form={form}
                          name="lastName"
                          label="Last Name"
                          placeholder="Enter last name"
                          validation={{ required: "Last name is required" }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="gender"
                        label="Gender"
                        placeholder="Select gender"
                        options={genderOptions}
                        validation={{ required: "Gender is required" }}
                      />
                      <DateInput
                        form={form}
                        name="dateOfBirth"
                        label="Date of Birth"
                        placeholder="YYYY-MM-DD"
                        validation={{ required: "Date of birth is required" }}
                      />
                      <SelectInput
                        form={form}
                        name="bloodGroup"
                        label="Blood Group"
                        placeholder="Select blood group"
                        options={bloodGroups}
                      />
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="maritalStatus"
                        label="Marital Status"
                        placeholder="Select marital status"
                        options={maritalStatuses}
                      />
                      <TextInput
                        form={form}
                        name="nationality"
                        label="Nationality"
                        placeholder="Enter nationality"
                        validation={{ required: "Nationality is required" }}
                      />
                      <TextInput
                        form={form}
                        name="religion"
                        label="Religion"
                        placeholder="Enter religion"
                      />
                    </div>
                  </div>

                  <div className="">
                    <div className="space-y-4">
                      <TextInput
                        form={form}
                        name="employeeId"
                        label="Employee ID"
                        placeholder="Enter employee ID"
                        validation={{ required: "Employee ID is required" }}
                      />
                      <TextInput
                        form={form}
                        name="aadharNumber"
                        label="Aadhar Number"
                        placeholder="Enter 12-digit Aadhar number"
                        validation={{
                          pattern: {
                            value: /^\d{12}$/,
                            message: "Aadhar number must be 12 digits",
                          },
                        }}
                      />
                      <TextInput
                        form={form}
                        name="panNumber"
                        label="PAN Number"
                        placeholder="Enter 10-digit PAN number"
                        validation={{
                          pattern: {
                            value: /^[A-Z]{5}\d{4}[A-Z]{1}$/,
                            message: "Invalid PAN number format",
                          },
                        }}
                      />
                    </div>
                  </div>

                  <FileInput
                    form={form}
                    name="profilePhoto"
                    label="Profile Photo"
                    description="Upload profile photo (PNG, JPG, JPEG) max size 5MB (Optional)"
                    accept="image/*"
                  />
                </div>
              </FormCard>

              <FormCard title="Contact Information" icon={Phone}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="personalEmail"
                    label="Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      required: "Email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PhoneInput
                    form={form}
                    name="phone"
                    label="Phone Number"
                    validation={{ required: "Phone number is required" }}
                  />
                  <PhoneInput
                    form={form}
                    name="alternatePhone"
                    label="Alternate Phone"
                  />
                  <SelectInput
                    form={form}
                    name="preferredContactMethod"
                    label="Preferred Contact Method"
                    placeholder="Select preferred contact method"
                    options={[
                      { label: "Email", value: "email" },
                      { label: "Phone", value: "phone" },
                    ]}
                  />
                </div>
              </FormCard>

              <FormCard title="Address Information" icon={MapPin}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="address"
                      label="Address"
                      placeholder="Enter permanent address"
                      inputProps={{ rows: 2 }}
                    />
                  </div>
                  <TextInput
                    form={form}
                    name="city"
                    label="City"
                    placeholder="Enter city"
                    validation={{ required: "City is required" }}
                  />
                  <TextInput
                    form={form}
                    name="state"
                    label="State"
                    placeholder="Enter state"
                    validation={{ required: "State is required" }}
                  />
                  <TextInput
                    form={form}
                    name="pincode"
                    label="Pincode"
                    placeholder="Enter pincode"
                    validation={{
                      required: "Pincode is required",
                      pattern: {
                        value: /^\d{6}$/,
                        message: "Pincode must be 6 digits",
                      },
                    }}
                  />
                  <TextInput
                    form={form}
                    name="country"
                    label="Country"
                    placeholder="Enter country"
                    validation={{ required: "Country is required" }}
                  />
                </div>
              </FormCard>

              <FormCard title="Professional Information" icon={Briefcase}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="designation"
                    label="Designation"
                    placeholder="Enter designation"
                    validation={{ required: "Designation is required" }}
                  />
                  <ComboboxInput
                    form={form}
                    name="department"
                    label="Department"
                    placeholder="Select department"
                    options={departmentOptions}
                    validation={{ required: "Department is required" }}
                    href="/dashboard/academics/departments"
                    toolTipText="Create a new department"
                  />
                  <ComboboxInput
                    form={form}
                    name="primarySubject"
                    label="Primary Subject"
                    placeholder="Select primary subject"
                    options={subjectOptions}
                    validation={{ required: "Primary subject is required" }}
                    href="/dashboard/academics/subjects"
                    toolTipText="Create a new subject"
                  />
                  <ComboboxInput
                    form={form}
                    name="secondarySubject"
                    label="Secondary Subject"
                    placeholder="Select secondary subject"
                    options={subjectOptions}
                    validation={{ required: "Secondary subject is required" }}
                    href="/dashboard/academics/subjects"
                    toolTipText="Create a new subject"
                  />
                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="qualification"
                        label="Highest Qualification"
                        placeholder="Select qualification"
                        options={qualifications}
                        validation={{ required: "Qualification is required" }}
                      />
                      <SelectInput
                        form={form}
                        name="employmentType"
                        label="Employment Type"
                        placeholder="Select employment type"
                        options={employmentTypes}
                        validation={{ required: "Employment type is required" }}
                      />
                      <SelectInput
                        form={form}
                        name="experience"
                        label="Teaching Experience"
                        placeholder="Select experience range"
                        options={teachingExperience}
                      />
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="previousExperience"
                      label="Previous Experience"
                      placeholder="Enter details of previous teaching experience"
                      inputProps={{ rows: 2 }}
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Emergency Contact" icon={User}>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <TextInput
                    form={form}
                    name="emergencyContactName"
                    label="Emergency Contact Name"
                    placeholder="Enter emergency contact name"
                    validation={{
                      required: "Emergency contact name is required",
                    }}
                  />
                  <PhoneInput
                    form={form}
                    name="emergencyContactPhone"
                    label="Emergency Contact Phone"
                    validation={{
                      required: "Emergency contact phone is required",
                    }}
                  />
                  <TextInput
                    form={form}
                    name="emergencyContactRelation"
                    label="Relationship"
                    placeholder="Enter relationship"
                    validation={{
                      required: "Emergency contact relationship is required",
                    }}
                  />
                </div>
              </FormCard>

              <FormCard title="System Access & Permissions" icon={Key}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="email"
                    label="Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      required: "Email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PasswordInput
                    form={form}
                    name="password"
                    label="Password"
                    placeholder="Enter password for login"
                    validation={{
                      required: editingId ? false : "Password is required",
                      minLength: {
                        value: 8,
                        message: "Password must be at least 8 characters",
                      },
                    }}
                  />
                  <div className="md:col-span-2">
                    <CheckboxInput
                      form={form}
                      name="isActive"
                      label="Active Account"
                      description="Enable this to allow the user to log in."
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Bank Details" icon={Briefcase}>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <TextInput
                    form={form}
                    name="bankName"
                    label="Bank Name"
                    placeholder="Enter bank name"
                  />
                  <TextInput
                    form={form}
                    name="bankAccountNumber"
                    label="Bank Account Number"
                    placeholder="Enter bank account number"
                  />
                  <TextInput
                    form={form}
                    name="ifscCode"
                    label="IFSC Code"
                    placeholder="Enter IFSC code"
                    validation={{
                      pattern: {
                        value: /^[A-Z]{4}0[A-Z0-9]{6}$/,
                        message: "Invalid IFSC code format",
                      },
                    }}
                  />
                  <div className="lg:col-span-3">
                    <TextareaInput
                      form={form}
                      name="additionalNotes"
                      label="Additional Notes"
                      placeholder="Enter additional notes"
                      inputProps={{ rows: 3 }}
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Additional Information" icon={FileText}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextareaInput
                    form={form}
                    name="specializations"
                    label="Specializations"
                    placeholder="Enter areas of specialization"
                    inputProps={{ rows: 2 }}
                  />
                  <TextareaInput
                    form={form}
                    name="achievements"
                    label="Achievements"
                    placeholder="Enter professional achievements and awards"
                    inputProps={{ rows: 2 }}
                  />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="notes"
                      label="Additional Notes"
                      placeholder="Enter any additional notes or comments"
                      inputProps={{ rows: 2 }}
                    />
                  </div>
                </div>
              </FormCard>
            </div>
          </div>
          <FormFooter
            href="/teachers"
            parent=""
            title="Teacher"
            editingId={editingId}
          />
        </form>
      </Form>
    </div>
  );
}
