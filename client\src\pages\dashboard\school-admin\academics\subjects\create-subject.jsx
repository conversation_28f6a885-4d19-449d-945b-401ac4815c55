import React, { useState, useEffect } from "react";
import { Container } from "@/components/ui/container";
import { SubjectForm } from "@/components/forms/dashboard/academics/subjects/subject-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useSubject } from "@/context/subject-context";
import { toast } from "sonner";

const CreateSubjects = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(!!id);
  const [subjectData, setSubjectData] = useState(null);
  const { fetchSubjectById } = useSubject();

  useEffect(() => {
    if (id) {
      const fetchData = async () => {
        try {
          const data = await fetchSubjectById(id);
          setSubjectData(data);
          setLoading(false);
        } catch (error) {
          console.error("Error fetching subject:", error);
          toast.error("Failed to load subject data");
          navigate("/dashboard/academics/subjects");
        }
      };

      fetchData();
    }
  }, [id, fetchSubjectById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Subject" : "Create New Subject"}
        actions={[
          {
            label: "Back to Subjects",
            href: "/dashboard/academics/subjects",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Academics", href: "/dashboard/academics" },
          { label: "Subjects", href: "/dashboard/academics/subjects" },
          { label: id ? "Edit Subject" : "Create Subject" },
        ]}
      />

      {id && loading ? (
        <FormCardSkeleton />
      ) : (
        <SubjectForm editingId={id} initialData={subjectData} />
      )}
    </Container>
  );
};

export default CreateSubjects;
