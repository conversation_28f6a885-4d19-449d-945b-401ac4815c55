import { createContext, useContext, useState, useEffect } from "react";
import {
  createFee,
  getFees,
  getFeeById,
  updateFee,
  deleteFee,
  updateFeeStatus,
  getFeesByClass,
  getFeesByTerm,
} from "@/api/fee-api";

const FeeContext = createContext({
  fees: [],
  currentFee: null,
  isLoading: false,
  error: null,
  addFee: () => {},
  fetchAllFees: () => {},
  fetchFeeById: () => {},
  editFee: () => {},
  removeFee: () => {},
  updateStatus: () => {},
  fetchFeesByClass: () => {},
  fetchFeesByTerm: () => {},
  feeOptions: [],
});

export const FeeProvider = ({ children }) => {
  const [fees, setFees] = useState([]);
  const [currentFee, setCurrentFee] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [feeOptions, setFeeOptions] = useState([]);

  useEffect(() => {
    const options = fees.map((fee) => ({
      label: `${fee.feeName} (${fee.feeCode})`,
      value: {
        id: fee._id,
        name: fee.feeName,
      },
    }));
    setFeeOptions(options);
  }, [fees]);

  const addFee = async (feeData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createFee(feeData);
      setFees((prevFees) => [...prevFees, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create fee");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllFees = async (filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getFees(filters);
      setFees(response.data || []);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || "Failed to fetch fees");
      setFees([]);
      setIsLoading(false);
      throw error;
    }
  };

  const fetchFeeById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getFeeById(id);
      const feeData = data.data || data;
      setCurrentFee(feeData);
      setIsLoading(false);
      return feeData;
    } catch (error) {
      setError(error.message || `Failed to fetch fee with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editFee = async (id, feeData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateFee(id, feeData);
      setFees(
        fees.map((fee) => (fee._id === id ? { ...fee, ...response.data } : fee))
      );
      if (currentFee && currentFee._id === id) {
        setCurrentFee({ ...currentFee, ...response.data });
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update fee with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeFee = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteFee(id);
      setFees(fees.filter((fee) => fee._id !== id));
      if (currentFee && currentFee._id === id) {
        setCurrentFee(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete fee with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, status) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await updateFeeStatus(id, status);
      setFees(
        fees.map((fee) =>
          fee._id === id ? { ...fee, status: data.data.status } : fee
        )
      );
      if (currentFee && currentFee._id === id) {
        setCurrentFee({ ...currentFee, status: data.data.status });
      }
      setIsLoading(false);
      return data;
    } catch (error) {
      setError(
        error.message || `Failed to update status for fee with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const fetchFeesByClass = async (classId, filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getFeesByClass(classId, filters);
      setFees(response.data || []);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch fees for class: ${classId}`);
      setFees([]);
      setIsLoading(false);
      throw error;
    }
  };

  const fetchFeesByTerm = async (termId, filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getFeesByTerm(termId, filters);
      setFees(response.data || []);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch fees for term: ${termId}`);
      setFees([]);
      setIsLoading(false);
      throw error;
    }
  };

  return (
    <FeeContext.Provider
      value={{
        fees,
        setFees,
        currentFee,
        isLoading,
        error,
        addFee,
        fetchAllFees,
        fetchFeeById,
        editFee,
        removeFee,
        updateStatus,
        fetchFeesByClass,
        fetchFeesByTerm,
        feeOptions,
      }}
    >
      {children}
    </FeeContext.Provider>
  );
};

export const useFee = () => useContext(FeeContext);
