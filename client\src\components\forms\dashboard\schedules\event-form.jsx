import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import { FileInput } from "@/components/form-inputs/file-input";
import {
  Calendar,
  MapPin,
  Users,
  Clock,
  Settings,
  FileText,
} from "lucide-react";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNavigate } from "react-router-dom";
import { useClass } from "@/context/class-context";
import { useSection } from "@/context/section-context";
import { useTeacher } from "@/context/teacher-context";
import { useEvent } from "@/context/event-context";

// Form options for events
const eventTypes = [
  { label: "Academic", value: "academic" },
  { label: "Sports", value: "sports" },
  { label: "Cultural", value: "cultural" },
  { label: "Competition", value: "competition" },
  { label: "Meeting", value: "meeting" },
  { label: "Celebration", value: "celebration" },
  { label: "Workshop", value: "workshop" },
  { label: "Seminar", value: "seminar" },
  { label: "Exam", value: "exam" },
  { label: "Holiday", value: "holiday" },
  { label: "Other", value: "other" },
];

const eventStatuses = [
  { label: "Scheduled", value: "scheduled" },
  { label: "In Progress", value: "in_progress" },
  { label: "Completed", value: "completed" },
  { label: "Cancelled", value: "cancelled" },
  { label: "Postponed", value: "postponed" },
];

const priorityLevels = [
  { label: "Low", value: "low" },
  { label: "Medium", value: "medium" },
  { label: "High", value: "high" },
  { label: "Urgent", value: "urgent" },
];

const attendeeTypes = [
  { label: "All Students", value: "all_students" },
  { label: "Specific Classes", value: "specific_classes" },
  { label: "Teachers Only", value: "teachers_only" },
  { label: "Parents Only", value: "parents_only" },
  { label: "Staff Only", value: "staff_only" },
  { label: "Mixed Group", value: "mixed_group" },
];

const venueTypes = [
  { label: "Auditorium", value: "auditorium" },
  { label: "Classroom", value: "classroom" },
  { label: "Sports Ground", value: "sports_ground" },
  { label: "Library", value: "library" },
  { label: "Laboratory", value: "laboratory" },
  { label: "Conference Room", value: "conference_room" },
  { label: "Online", value: "online" },
  { label: "External Venue", value: "external" },
  { label: "Other", value: "other" },
];

export function EventForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { classOptions, fetchAllClasses } = useClass();
  const { sectionOptions, fetchAllSections } = useSection();
  const { teacherOptions, fetchAllTeachers } = useTeacher();
  const { addEvent, editEvent } = useEvent();

  const form = useForm({
    defaultValues: {
      // Basic Event Information
      title: initialData?.title || "",
      description: initialData?.description || "",
      eventType: initialData?.eventType || "",
      priority: initialData?.priority || "medium",
      status: initialData?.status || "scheduled",

      // Date and Time Information
      startDate: initialData?.startDate
        ? new Date(initialData.startDate).toISOString().split("T")[0]
        : "",
      endDate: initialData?.endDate
        ? new Date(initialData.endDate).toISOString().split("T")[0]
        : "",
      startTime: initialData?.startTime || "",
      endTime: initialData?.endTime || "",
      isAllDayEvent: initialData?.isAllDayEvent || false,

      // Location Information
      venueType: initialData?.venueType || "",
      venueName: initialData?.venueName || "",
      venueAddress: initialData?.venueAddress || "",
      roomNumber: initialData?.roomNumber || "",
      capacity: initialData?.capacity || "",
      onlineLink: initialData?.onlineLink || "",

      // Attendee Information
      attendeeType: initialData?.attendeeType || "",
      targetClasses:
        initialData?.targetClasses?.map((cls) => cls._id || cls) || [],
      targetSections:
        initialData?.targetSections?.map((sec) => sec._id || sec) || [],
      organizer: initialData?.organizer?._id || initialData?.organizer || "",
      coordinator:
        initialData?.coordinator?._id || initialData?.coordinator || "",
      expectedAttendees: initialData?.expectedAttendees || "",

      // Additional Information
      requirements: initialData?.requirements || "",
      budget: initialData?.budget || "",
      instructions: initialData?.instructions || "",
      dresscode: initialData?.dresscode || "",
      materialsNeeded: initialData?.materialsNeeded || "",

      // File Attachments
      eventBanner: initialData?.eventBanner || null,
      attachments: initialData?.attachments || null,

      // Additional Fields
      registrationRequired: initialData?.registrationRequired || false,
      registrationDeadline: initialData?.registrationDeadline || "",
      contactPerson: initialData?.contactPerson || "",
      contactPhone: initialData?.contactPhone || "",
      contactEmail: initialData?.contactEmail || "",
      tags: initialData?.tags || "",
      notes: initialData?.notes || "",
    },
  });

  useEffect(() => {
    fetchAllClasses();
    fetchAllSections();
    fetchAllTeachers();
  }, []);

  const watchedVenueType = form.watch("venueType");
  const watchedAttendeeType = form.watch("attendeeType");
  const watchedIsAllDay = form.watch("isAllDayEvent");
  const watchedRegistrationRequired = form.watch("registrationRequired");

  const onSubmit = async (data) => {
    try {
      if (editingId) {
        await editEvent(editingId, data);
        toast.success("Event has been updated successfully.", {
          description: `${data.title} has been updated.`,
        });
        navigate("/dashboard/schedules/events");
      } else {
        await addEvent(data);
        toast.success("New event has been created successfully.", {
          description: `${data.title} has been added to the schedule.`,
        });
      }
    } catch (error) {
      console.error("Error submitting event form:", error);
      toast.error("Error", {
        description:
          error.message ||
          "Failed to save event information. Please try again.",
      });
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <div className="space-y-6">
              {/* Basic Event Information */}
              <FormCard title="Event Information" icon={Calendar}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <TextInput
                      form={form}
                      name="title"
                      label="Event Title"
                      placeholder="Enter event title"
                      validation={{
                        required: "Event title is required",
                        maxLength: {
                          value: 100,
                          message: "Title cannot exceed 100 characters",
                        },
                      }}
                    />
                  </div>

                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="description"
                      label="Event Description"
                      placeholder="Describe the event details, purpose, and agenda"
                      inputProps={{ rows: 4 }}
                      validation={{
                        required: "Event description is required",
                        maxLength: {
                          value: 500,
                          message: "Description cannot exceed 500 characters",
                        },
                      }}
                    />
                  </div>

                  <SelectInput
                    form={form}
                    name="eventType"
                    label="Event Type"
                    placeholder="Select event type"
                    options={eventTypes}
                    validation={{ required: "Event type is required" }}
                  />

                  <SelectInput
                    form={form}
                    name="priority"
                    label="Priority Level"
                    placeholder="Select priority"
                    options={priorityLevels}
                    validation={{ required: "Priority is required" }}
                  />

                  <SelectInput
                    form={form}
                    name="status"
                    label="Event Status"
                    placeholder="Select status"
                    options={eventStatuses}
                    validation={{ required: "Status is required" }}
                  />

                  <TextInput
                    form={form}
                    name="tags"
                    label="Tags"
                    placeholder="e.g., annual, mandatory, optional"
                    description="Comma-separated tags for better organization"
                  />
                </div>
              </FormCard>

              {/* Date and Time Information */}
              <FormCard title="Date & Time" icon={Clock}>
                <div className="space-y-4">
                  <CheckboxInput
                    form={form}
                    name="isAllDayEvent"
                    label="All-day event"
                    description="This event runs for the entire day"
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <DateInput
                      form={form}
                      name="startDate"
                      label="Start Date"
                      validation={{ required: "Start date is required" }}
                    />

                    <DateInput
                      form={form}
                      name="endDate"
                      label="End Date"
                      validation={{
                        required: "End date is required",
                        validate: (value) => {
                          const startDate = form.getValues("startDate");
                          if (startDate && value < startDate) {
                            return "End date must be after start date";
                          }
                          return true;
                        },
                      }}
                    />
                  </div>

                  {!watchedIsAllDay && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <TextInput
                        form={form}
                        name="startTime"
                        label="Start Time"
                        type="time"
                        validation={{ required: "Start time is required" }}
                      />

                      <TextInput
                        form={form}
                        name="endTime"
                        label="End Time"
                        type="time"
                        validation={{
                          required: "End time is required",
                          validate: (value) => {
                            const startTime = form.getValues("startTime");
                            const startDate = form.getValues("startDate");
                            const endDate = form.getValues("endDate");

                            if (
                              startDate === endDate &&
                              startTime &&
                              value <= startTime
                            ) {
                              return "End time must be after start time";
                            }
                            return true;
                          },
                        }}
                      />
                    </div>
                  )}
                </div>
              </FormCard>

              {/* Location Information */}
              <FormCard title="Venue & Location" icon={MapPin}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SelectInput
                    form={form}
                    name="venueType"
                    label="Venue Type"
                    placeholder="Select venue type"
                    options={venueTypes}
                    validation={{ required: "Venue type is required" }}
                  />

                  <TextInput
                    form={form}
                    name="venueName"
                    label="Venue Name"
                    placeholder="Enter venue name"
                    validation={{ required: "Venue name is required" }}
                  />

                  {watchedVenueType === "external" && (
                    <div className="md:col-span-2">
                      <TextareaInput
                        form={form}
                        name="venueAddress"
                        label="Venue Address"
                        placeholder="Enter complete venue address"
                        inputProps={{ rows: 2 }}
                        validation={{ required: "Venue address is required" }}
                      />
                    </div>
                  )}

                  {watchedVenueType === "online" && (
                    <div className="md:col-span-2">
                      <TextInput
                        form={form}
                        name="onlineLink"
                        label="Meeting Link"
                        placeholder="Enter meeting/conference link"
                        validation={{
                          required:
                            "Meeting link is required for online events",
                          pattern: {
                            value: /^https?:\/\/.+/,
                            message: "Please enter a valid URL",
                          },
                        }}
                      />
                    </div>
                  )}

                  <TextInput
                    form={form}
                    name="roomNumber"
                    label="Room/Hall Number"
                    placeholder="e.g., Room 101, Hall A"
                  />

                  <TextInput
                    form={form}
                    name="capacity"
                    label="Venue Capacity"
                    type="number"
                    placeholder="Maximum attendees"
                    validation={{
                      pattern: {
                        value: /^\d+$/,
                        message: "Capacity must be a number",
                      },
                    }}
                  />
                </div>
              </FormCard>

              {/* Attendee Information */}
              <FormCard title="Attendees & Participants" icon={Users}>
                <div className="space-y-4">
                  <SelectInput
                    form={form}
                    name="attendeeType"
                    label="Target Audience"
                    placeholder="Select target audience"
                    options={attendeeTypes}
                    validation={{ required: "Target audience is required" }}
                  />

                  {watchedAttendeeType === "specific_classes" && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <ComboboxInput
                        form={form}
                        name="targetClasses"
                        label="Target Classes"
                        placeholder="Select classes"
                        options={classOptions}
                        multiple={true}
                        validation={{
                          required: "At least one class is required",
                        }}
                        href="/dashboard/academics/classes"
                        toolTipText="Create a new class"
                      />

                      <ComboboxInput
                        form={form}
                        name="targetSections"
                        label="Target Sections"
                        placeholder="Select sections"
                        options={sectionOptions}
                        multiple={true}
                        href="/dashboard/academics/sections"
                        toolTipText="Create a new section"
                      />
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <ComboboxInput
                      form={form}
                      name="organizer"
                      label="Event Organizer"
                      placeholder="Select organizer"
                      options={teacherOptions}
                      validation={{ required: "Event organizer is required" }}
                      href="/dashboard/teachers"
                      toolTipText="Add a new teacher"
                    />

                    <ComboboxInput
                      form={form}
                      name="coordinator"
                      label="Event Coordinator"
                      placeholder="Select coordinator"
                      options={teacherOptions}
                      href="/dashboard/teachers"
                      toolTipText="Add a new teacher"
                    />
                  </div>

                  <TextInput
                    form={form}
                    name="expectedAttendees"
                    label="Expected Number of Attendees"
                    type="number"
                    placeholder="Estimated attendee count"
                    validation={{
                      pattern: {
                        value: /^\d+$/,
                        message: "Must be a valid number",
                      },
                    }}
                  />
                </div>
              </FormCard>

              {/* Registration Information */}
              <FormCard title="Registration & Contact" icon={FileText}>
                <div className="space-y-4">
                  <CheckboxInput
                    form={form}
                    name="registrationRequired"
                    label="Registration required"
                    description="Attendees must register before the event"
                  />

                  {watchedRegistrationRequired && (
                    <DateInput
                      form={form}
                      name="registrationDeadline"
                      label="Registration Deadline"
                      validation={{
                        required: "Registration deadline is required",
                      }}
                    />
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <TextInput
                      form={form}
                      name="contactPerson"
                      label="Contact Person"
                      placeholder="Person to contact for queries"
                    />

                    <TextInput
                      form={form}
                      name="contactPhone"
                      label="Contact Phone"
                      placeholder="Phone number"
                      validation={{
                        pattern: {
                          value: /^[0-9+\-\s()]+$/,
                          message: "Please enter a valid phone number",
                        },
                      }}
                    />

                    <TextInput
                      form={form}
                      name="contactEmail"
                      label="Contact Email"
                      type="email"
                      placeholder="<EMAIL>"
                      validation={{
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: "Please enter a valid email address",
                        },
                      }}
                    />
                  </div>
                </div>
              </FormCard>

              {/* File Attachments */}
              <FormCard title="Attachments" icon={FileText}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FileInput
                    form={form}
                    name="eventBanner"
                    label="Event Banner"
                    description="Upload event banner or poster (PNG, JPG, JPEG) max 5MB"
                    accept="image/*"
                  />

                  <FileInput
                    form={form}
                    name="attachments"
                    label="Additional Files"
                    description="Upload related documents (PDF, DOC, etc.) max 10MB"
                    accept=".pdf,.doc,.docx,.txt"
                  />
                </div>
              </FormCard>

              {/* Additional Information */}
              <FormCard title="Additional Details" icon={Settings}>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <TextInput
                      form={form}
                      name="budget"
                      label="Event Budget"
                      type="number"
                      placeholder="Budget amount"
                      description="Budget allocated for this event"
                    />

                    <TextInput
                      form={form}
                      name="dresscode"
                      label="Dress Code"
                      placeholder="e.g., Formal, Casual, Uniform"
                      description="Required dress code for attendees"
                    />
                  </div>

                  <TextareaInput
                    form={form}
                    name="requirements"
                    label="Event Requirements"
                    placeholder="List any special requirements (equipment, setup, etc.)"
                    inputProps={{ rows: 3 }}
                  />

                  <TextareaInput
                    form={form}
                    name="materialsNeeded"
                    label="Materials Needed"
                    placeholder="List materials attendees should bring"
                    inputProps={{ rows: 3 }}
                  />

                  <TextareaInput
                    form={form}
                    name="instructions"
                    label="Special Instructions"
                    placeholder="Any special instructions for attendees"
                    inputProps={{ rows: 3 }}
                  />

                  <TextareaInput
                    form={form}
                    name="notes"
                    label="Additional Notes"
                    placeholder="Any additional notes or comments"
                    inputProps={{ rows: 3 }}
                  />
                </div>
              </FormCard>
            </div>
          </div>

          <FormFooter
            href="/events"
            parent="schedules"
            title="Event"
            editingId={editingId}
          />
        </form>
      </Form>
    </div>
  );
}

export default EventForm;
