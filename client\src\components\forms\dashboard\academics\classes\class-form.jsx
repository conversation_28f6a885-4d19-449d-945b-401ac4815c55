import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import { SwitchInput } from "@/components/form-inputs/switch-input";
import { FileText, Book } from "lucide-react";
import { SelectInput } from "@/components/form-inputs/select-input";
import { academicYears } from "@/utils/form-options";
import { useClass } from "@/context/class-context";
import { useDepartment } from "@/context/department-context";

export function ClassForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { addClass, editClass } = useClass();
  const { fetchAllDepartments, departmentOptions } = useDepartment();

  const form = useForm({
    defaultValues: {
      // Basic Information
      name: initialData?.name || "",
      code: initialData?.code || "",
      academicYear: initialData?.academicYear || "",
      department: initialData?.department || "",

      // Additional Information
      description: initialData?.description || "",
      isActive: initialData?.isActive || true,
    },
  });

  useEffect(() => {
    fetchAllDepartments();
  }, [fetchAllDepartments]);

  const handleSubmit = async (data) => {
    try {
      if (editingId) {
        await editClass(editingId, data);
        toast.success("Class updated successfully.", {
          description: `${data.name} has been updated.`,
        });
        navigate("/dashboard/academics/classes");
      } else {
        await addClass(data);
        toast.success("Class created successfully.", {
          description: `${data.name} has been added to the class list.`,
        });
      }
    } catch (error) {
      console.error("Class form error:", error);
      toast.error(
        error.response?.data?.message || error.message || "Submission failed."
      );
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormCard title="Class Information" icon={Book}>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <TextInput
                form={form}
                name="name"
                label="Class Name"
                placeholder="e.g., Class 10 A"
                validation={{ required: "Class name is required" }}
              />
              <TextInput
                form={form}
                name="code"
                label="Class Code"
                placeholder="e.g., C10A"
                validation={{ required: "Class code is required" }}
              />
              <SelectInput
                form={form}
                name="academicYear"
                label="Academic Year"
                options={academicYears}
                placeholder="Select academic year"
                validation={{ required: "Academic year is required" }}
              />
              <ComboboxInput
                form={form}
                name="department"
                label="Department"
                options={departmentOptions}
                placeholder="Select department"
                validation={{ required: "Department is required" }}
                href="/dashboard/academics/departments"
                toolTipText="Add new Department"
              />
            </div>
          </FormCard>

          <FormCard title="Additional Information" icon={FileText}>
            <div className="grid grid-cols-1 gap-4">
              <TextareaInput
                form={form}
                name="description"
                label="Description"
                placeholder="Add class notes or details"
                inputProps={{ rows: 3 }}
              />
              <SwitchInput form={form} name="isActive" label="Is Active" />
            </div>
          </FormCard>

          <FormFooter
            href="/academics/classes"
            editingId={editingId}
            title="Class"
          />
        </form>
      </Form>
    </div>
  );
}
