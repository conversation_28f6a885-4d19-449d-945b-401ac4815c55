import express from "express";
import {
  createClass,
  getAllClasses,
  getClassById,
  updateClass,
  deleteClass,
  updateClassStatus,
  getClassesByDepartment,
  getClassesByAcademicYear,
} from "../controllers/class.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createClass
);

router.get(
  "/",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getAllClasses
);

router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getClassById
);

router.put("/:id", protect, authorize(["admin", "school-admin"]), updateClass);

router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteClass
);

router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateClassStatus
);

router.get(
  "/department/:departmentId",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getClassesByDepartment
);

router.get(
  "/academic-year/:academicYear",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getClassesByAcademicYear
);

export default router;
