import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import { FileText } from "lucide-react";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SwitchInput } from "@/components/form-inputs/switch-input";
import { useSection } from "@/context/section-context";
import { useClass } from "@/context/class-context";
import { useTeacher } from "@/context/teacher-context";

export function SectionForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { addSection, editSection } = useSection();
  const { fetchAllClasses, classOptions } = useClass();
  const { fetchAllTeachers, teacherOptions } = useTeacher();

  const form = useForm({
    defaultValues: {
      // Basic Information
      name: initialData?.name || "",
      code: initialData?.code || "",
      classId: initialData?.classId || "",
      teacherId: initialData?.teacherId || "",

      // Additional Information
      description: initialData?.description || "",
      isActive: initialData?.isActive ?? true,
    },
  });

  useEffect(() => {
    fetchAllClasses();
    fetchAllTeachers();
  }, []);

  const handleSubmit = async (data) => {
    try {
      if (editingId) {
        await editSection(editingId, data);
        toast.success("Setion updated successfully.", {
          description: `${data.name} has been updated.`,
        });
        navigate("/dashboard/academics/sections");
      } else {
        await addSection(data);
        toast.success("Section created successfully.", {
          description: `${data.name} has been added to the section list.`,
        });
      }
    } catch (error) {
      console.error("Section form error:", error);
      toast.error(
        error.response?.data?.message || error.message || "Submission failed."
      );
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormCard title="Section Information" icon={FileText}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <TextInput
                form={form}
                name="name"
                label="Section Name"
                placeholder="e.g., A"
                validation={{ required: "Section name is required" }}
              />
              <TextInput
                form={form}
                name="code"
                label="Section Code"
                placeholder="e.g., A1"
                validation={{ required: "Section code is required" }}
              />
              <ComboboxInput
                form={form}
                name="classId"
                label="Class"
                options={classOptions}
                placeholder="Select class"
                validation={{ required: "Class is required" }}
                href="/dashboard/academics/classes"
                toolTipText="Add new class"
              />
              <ComboboxInput
                form={form}
                name="teacherId"
                label="Teacher"
                placeholder="Select teacher"
                options={teacherOptions}
                validation={{ required: "Teacher is required" }}
                href="/dashboard/users?role=teacher"
                toolTipText="Add new teacher"
              />
            </div>
          </FormCard>

          <FormCard title="Additional Information" icon={FileText}>
            <div className="grid grid-cols-1 gap-4">
              <TextareaInput
                form={form}
                name="description"
                label="Description"
                placeholder="Enter additional notes about this section"
                inputProps={{ rows: 3 }}
              />
              <SwitchInput form={form} name="isActive" label="Is Active" />
            </div>
          </FormCard>

          <FormFooter
            href="/academics/classes"
            editingId={editingId}
            title="Section"
          />
        </form>
      </Form>
    </div>
  );
}
