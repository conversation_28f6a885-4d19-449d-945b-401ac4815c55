import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { PhoneInput } from "@/components/form-inputs/phone-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import {
  User,
  GraduationCap,
  MapPin,
  BookOpen,
  FileText,
  Key,
  Phone,
  Users,
  CreditCard,
} from "lucide-react";
import {
  genderOptions,
  bloodGroups,
  categories,
  admissionTypes,
  transportModes,
  studentStatuses,
  academicYears,
  feeStructures,
} from "@/utils/form-options";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNavigate } from "react-router-dom";
import { PasswordInput } from "@/components/form-inputs/password-input";
import { FileInput } from "@/components/form-inputs/file-input";
import { useClass } from "@/context/class-context";
import { useSection } from "@/context/section-context";
import { useParent } from "@/context/parent-context";
import { useStudent } from "@/context/student-context";

export function StudentForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { fetchAllClasses, classOptions } = useClass();
  const { fetchAllSections, sectionOptions } = useSection();
  const { fetchAllParents, parentOptions } = useParent();
  const { addStudent, editStudent } = useStudent();

  const form = useForm({
    defaultValues: {
      // Personal Information
      firstName: initialData?.firstName || "",
      lastName: initialData?.lastName || "",
      dateOfBirth: initialData?.dateOfBirth || "",
      gender: initialData?.gender || "",
      bloodGroup: initialData?.bloodGroup || "",
      nationality: initialData?.nationality || "Indian",
      religion: initialData?.religion || "",
      parent: initialData?.parent || "",
      aadharNumber: initialData?.aadharNumber || "",
      studentId: initialData?.studentId || "",
      rollNumber: initialData?.rollNumber || "",

      // Address Information
      address: initialData?.address || "",
      city: initialData?.city || "",
      state: initialData?.state || "",
      pincode: initialData?.pincode || "",
      country: initialData?.country || "India",

      // Contact Information
      personalEmail: initialData?.personalEmail || "",
      phone: initialData?.phone || "",
      alternatePhone: initialData?.alternatePhone || "",
      preferredContactMethod: initialData?.preferredContactMethod || "email",

      // Academic Information
      class: initialData?.class || "",
      section: initialData?.section || "",
      academicYear: initialData?.academicYear || "",
      admissionDate: initialData?.admissionDate || "",
      admissionType: initialData?.admissionType || "",
      previousSchool: initialData?.previousSchool || "",

      // Emergency Contact
      emergencyContactName: initialData?.emergencyContactName || "",
      emergencyContactPhone: initialData?.emergencyContactPhone || "",
      emergencyContactRelation: initialData?.emergencyContactRelation || "",

      // System Access & Permissions
      email: initialData?.email || "",
      password: initialData?.password || "",
      isActive: initialData?.isActive || true,

      // Fee & Financial Information
      feeStructure: initialData?.feeStructure || "",
      scholarshipApplied: initialData?.scholarshipApplied || false,
      scholarshipAmount: initialData?.scholarshipAmount || "",
      transportRequired: initialData?.transportRequired || false,
      transportMode: initialData?.transportMode || "",
      busRoute: initialData?.busRoute || "",

      // Medical Information
      medicalConditions: initialData?.medicalConditions || "",
      allergies: initialData?.allergies || "",
      medications: initialData?.medications || "",
      doctorName: initialData?.doctorName || "",
      doctorPhone: initialData?.doctorPhone || "",

      // Additional Information
      hobbies: initialData?.hobbies || "",
      achievements: initialData?.achievements || "",
      specialNeeds: initialData?.specialNeeds || "",
      additionalNotes: initialData?.additionalNotes || "",
      status: initialData?.status || "active",

      // File uploads
      profilePhoto: initialData?.profilePhoto || null,
      birthCertificate: initialData?.birthCertificate || null,
      addressProof: initialData?.addressProof || null,
      medicalCertificate: initialData?.medicalCertificate || null,
    },
  });

  useEffect(() => {
    fetchAllClasses();
    fetchAllSections();
    fetchAllParents();
  }, []);

  const onSubmit = async (data) => {
    try {
      if (editingId) {
        await editStudent(editingId, data);
        toast.success("Student information has been updated successfully.", {
          description: `${data.firstName} ${data.lastName} has been updated.`,
        });
        navigate("/dashboard/students");
      } else {
        await addStudent(data);
        toast.success("New student has been created successfully.", {
          description: `${data.firstName} ${data.lastName} has been added to the student list.`,
        });
      }
    } catch (error) {
      console.error("Error submitting student form:", error);
      toast.error({
        title: "Error",
        description:
          error.message ||
          "Failed to save student information. Please try again.",
      });
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            <div className="space-y-6">
              <FormCard title="Personal Information" icon={User}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <TextInput
                        form={form}
                        name="firstName"
                        label="First Name"
                        placeholder="Enter first name"
                        validation={{ required: "First name is required" }}
                      />
                      <TextInput
                        form={form}
                        name="lastName"
                        label="Last Name"
                        placeholder="Enter last name"
                        validation={{ required: "Last name is required" }}
                      />
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="gender"
                        label="Gender"
                        placeholder="Select gender"
                        options={genderOptions}
                        validation={{ required: "Gender is required" }}
                      />
                      <DateInput
                        form={form}
                        name="dateOfBirth"
                        label="Date of Birth"
                        placeholder="YYYY-MM-DD"
                        validation={{ required: "Date of birth is required" }}
                      />
                      <SelectInput
                        form={form}
                        name="bloodGroup"
                        label="Blood Group"
                        placeholder="Select blood group"
                        options={bloodGroups}
                      />
                    </div>
                  </div>

                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      <TextInput
                        form={form}
                        name="nationality"
                        label="Nationality"
                        placeholder="Enter nationality"
                        validation={{ required: "Nationality is required" }}
                      />
                      <TextInput
                        form={form}
                        name="religion"
                        label="Religion"
                        placeholder="Select religion"
                      />
                      <ComboboxInput
                        form={form}
                        name="parent"
                        label="Parent/Guardian"
                        placeholder="Select parent"
                        options={parentOptions}
                        validation={{ required: "Parent is required" }}
                        href="/dashboard/parents"
                        toolTipText="Create a new parent"
                      />
                    </div>
                  </div>

                  <div className="">
                    <div className="space-y-4">
                      <TextInput
                        form={form}
                        name="studentId"
                        label="Student ID"
                        placeholder="Enter student ID"
                        validation={{ required: "Student ID is required" }}
                      />
                      <TextInput
                        form={form}
                        name="rollNumber"
                        label="Roll Number"
                        placeholder="Enter roll number"
                        validation={{ required: "Roll number is required" }}
                      />
                      <TextInput
                        form={form}
                        name="aadharNumber"
                        label="Aadhar Number"
                        placeholder="Enter 12-digit Aadhar number"
                        validation={{
                          pattern: {
                            value: /^\d{12}$/,
                            message: "Aadhar number must be 12 digits",
                          },
                        }}
                      />
                    </div>
                  </div>

                  <FileInput
                    form={form}
                    name="profilePhoto"
                    label="Profile Photo"
                    description="Upload profile photo (PNG, JPG, JPEG) max size 5MB (Optional)"
                    accept="image/*"
                  />
                </div>
              </FormCard>

              <FormCard title="Contact Information" icon={Phone}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="personalEmail"
                    label="Student Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PhoneInput
                    form={form}
                    name="phone"
                    label="Student Phone Number"
                  />
                  <PhoneInput
                    form={form}
                    name="alternatePhone"
                    label="Alternate Phone"
                  />
                  <SelectInput
                    form={form}
                    name="preferredContactMethod"
                    label="Preferred Contact Method"
                    placeholder="Select preferred contact method"
                    options={[
                      { label: "Email", value: "email" },
                      { label: "Phone", value: "phone" },
                      { label: "Parent Phone", value: "parent_phone" },
                    ]}
                  />
                </div>
              </FormCard>

              <FormCard title="Address Information" icon={MapPin}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="address"
                      label="Address"
                      placeholder="Enter permanent address"
                      inputProps={{ rows: 2 }}
                      validation={{ required: "Address is required" }}
                    />
                  </div>
                  <TextInput
                    form={form}
                    name="city"
                    label="City"
                    placeholder="Enter city"
                    validation={{ required: "City is required" }}
                  />
                  <TextInput
                    form={form}
                    name="state"
                    label="State"
                    placeholder="Enter state"
                    validation={{ required: "State is required" }}
                  />
                  <TextInput
                    form={form}
                    name="pincode"
                    label="Pincode"
                    placeholder="Enter pincode"
                    validation={{
                      required: "Pincode is required",
                      pattern: {
                        value: /^\d{6}$/,
                        message: "Pincode must be 6 digits",
                      },
                    }}
                  />
                  <TextInput
                    form={form}
                    name="country"
                    label="Country"
                    placeholder="Enter country"
                    validation={{ required: "Country is required" }}
                  />
                </div>
              </FormCard>

              <FormCard title="Academic Information" icon={GraduationCap}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ComboboxInput
                    form={form}
                    name="class"
                    label="Class"
                    placeholder="Select class"
                    options={classOptions}
                    validation={{ required: "Class is required" }}
                    href="/dashboard/academics/classes"
                    toolTipText="Create a new class"
                  />
                  <ComboboxInput
                    form={form}
                    name="section"
                    label="Section"
                    placeholder="Select section"
                    options={sectionOptions}
                    validation={{ required: "Section is required" }}
                    href="/dashboard/academics/sections"
                    toolTipText="Create a new section"
                  />
                  <SelectInput
                    form={form}
                    name="academicYear"
                    label="Academic Year"
                    options={academicYears}
                    placeholder="2024-2025"
                    validation={{ required: "Academic year is required" }}
                  />
                  <DateInput
                    form={form}
                    name="admissionDate"
                    label="Admission Date"
                    placeholder="YYYY-MM-DD"
                    validation={{ required: "Admission date is required" }}
                  />
                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="admissionType"
                        label="Admission Type"
                        placeholder="Select admission type"
                        options={admissionTypes}
                        validation={{ required: "Admission type is required" }}
                      />
                      <SelectInput
                        form={form}
                        name="status"
                        label="Student Status"
                        placeholder="Select student status"
                        options={studentStatuses}
                        validation={{ required: "Student status is required" }}
                      />
                      <TextInput
                        form={form}
                        name="previousSchool"
                        label="Previous School"
                        placeholder="Enter previous school name"
                      />
                    </div>
                  </div>
                </div>
              </FormCard>

              <FormCard title="Emergency Contact" icon={Phone}>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <TextInput
                    form={form}
                    name="emergencyContactName"
                    label="Emergency Contact Name"
                    placeholder="Enter emergency contact name"
                    validation={{
                      required: "Emergency contact name is required",
                    }}
                  />
                  <PhoneInput
                    form={form}
                    name="emergencyContactPhone"
                    label="Emergency Contact Phone"
                    validation={{
                      required: "Emergency contact phone is required",
                    }}
                  />
                  <TextInput
                    form={form}
                    name="emergencyContactRelation"
                    label="Relationship"
                    placeholder="Enter relationship"
                    validation={{
                      required: "Emergency contact relationship is required",
                    }}
                  />
                </div>
              </FormCard>

              <FormCard title="System Access & Permissions" icon={Key}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="email"
                    label="System Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      required: "System email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PasswordInput
                    form={form}
                    name="password"
                    label="Password"
                    placeholder="Enter password for login"
                    validation={{
                      required: editingId ? false : "Password is required",
                      minLength: {
                        value: 8,
                        message: "Password must be at least 8 characters",
                      },
                    }}
                  />
                  <div className="md:col-span-2">
                    <CheckboxInput
                      form={form}
                      name="isActive"
                      label="Active Account"
                      description="Enable this to allow the student to log in to the system."
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Fee & Transportation" icon={CreditCard}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SelectInput
                    form={form}
                    name="feeStructure"
                    label="Fee Structure"
                    options={feeStructures}
                    placeholder="Enter fee structure"
                  />
                  <CheckboxInput
                    form={form}
                    name="scholarshipApplied"
                    label="Scholarship Applied"
                    description="Check if scholarship has been applied."
                  />
                  <TextInput
                    form={form}
                    name="scholarshipAmount"
                    label="Scholarship Amount"
                    placeholder="Enter scholarship amount"
                    type="number"
                  />
                  <CheckboxInput
                    form={form}
                    name="transportRequired"
                    label="Transportation Required"
                    description="Check if student requires school transportation."
                  />
                  <SelectInput
                    form={form}
                    name="transportMode"
                    label="Transport Mode"
                    placeholder="Select transport mode"
                    options={transportModes}
                  />
                  <TextInput
                    form={form}
                    name="busRoute"
                    label="Bus Route"
                    placeholder="Enter bus route"
                  />
                </div>
              </FormCard>

              <FormCard title="Medical Information" icon={FileText}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextareaInput
                    form={form}
                    name="medicalConditions"
                    label="Medical Conditions"
                    placeholder="Enter any medical conditions"
                    inputProps={{ rows: 2 }}
                  />
                  <TextareaInput
                    form={form}
                    name="allergies"
                    label="Allergies"
                    placeholder="Enter any allergies"
                    inputProps={{ rows: 2 }}
                  />
                  <TextareaInput
                    form={form}
                    name="medications"
                    label="Current Medications"
                    placeholder="Enter current medications"
                    inputProps={{ rows: 2 }}
                  />
                  <div className="space-y-4">
                    <TextInput
                      form={form}
                      name="doctorName"
                      label="Family Doctor Name"
                      placeholder="Enter family doctor name"
                    />
                    <PhoneInput
                      form={form}
                      name="doctorPhone"
                      label="Doctor Phone"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <FileInput
                      form={form}
                      name="medicalCertificate"
                      label="Medical Certificate"
                      description="Upload medical certificate (PDF, JPG, PNG) max size 10MB"
                      accept=".pdf,.jpg,.jpeg,.png"
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Additional Information" icon={BookOpen}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextareaInput
                    form={form}
                    name="hobbies"
                    label="Hobbies & Interests"
                    placeholder="Enter hobbies and interests"
                    inputProps={{ rows: 2 }}
                  />
                  <TextareaInput
                    form={form}
                    name="achievements"
                    label="Achievements"
                    placeholder="Enter achievements and awards"
                    inputProps={{ rows: 2 }}
                  />
                  <TextareaInput
                    form={form}
                    name="specialNeeds"
                    label="Special Needs"
                    placeholder="Enter any special needs or requirements"
                    inputProps={{ rows: 2 }}
                  />
                  <TextareaInput
                    form={form}
                    name="additionalNotes"
                    label="Additional Notes"
                    placeholder="Enter any additional notes"
                    inputProps={{ rows: 2 }}
                  />
                </div>
              </FormCard>

              <FormCard title="Document Uploads" icon={FileText}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FileInput
                    form={form}
                    name="birthCertificate"
                    label="Birth Certificate"
                    description="Upload birth certificate (PDF, JPG, PNG) max size 10MB"
                    accept=".pdf,.jpg,.jpeg,.png"
                  />
                  <FileInput
                    form={form}
                    name="addressProof"
                    label="Address Proof"
                    description="Upload address proof (PDF, JPG, PNG) max size 10MB"
                    accept=".pdf,.jpg,.jpeg,.png"
                  />
                </div>
              </FormCard>
            </div>
          </div>
          <FormFooter
            href="/students"
            parent=""
            title="Student"
            editingId={editingId}
          />
        </form>
      </Form>
    </div>
  );
}
