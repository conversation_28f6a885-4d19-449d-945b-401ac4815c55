import { useState, useEffect } from "react";
import { Menu } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetDescription,
  SheetTrigger,
} from "@/components/ui/sheet";
import { TermList } from "./term-list";
import { TermDetails } from "./term-details";
import { useTerm } from "@/context/term-context";
import { toast } from "sonner";

export default function TermManagement() {
  const [terms, setTerms] = useState([]);
  const [selectedTerm, setSelectedTerm] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const { fetchAllTerms } = useTerm();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await fetchAllTerms();
        setTerms(response.data || []);
      } catch (error) {
        console.error("Error fetching terms:", error);
        toast.error("Error", {
          description: error.message || "Failed to fetch terms",
        });
        setTerms([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredTerms =
    terms?.filter(
      (term) =>
        term?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        term?.code?.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  const handleSelectTerm = (term) => {
    setSelectedTerm(term);
    setIsSheetOpen(false);
  };

  const handleTermDeleted = (deletedTermId) => {
    setTerms((prev) => prev.filter((term) => term._id !== deletedTermId));

    if (selectedTerm?._id === deletedTermId) {
      setSelectedTerm(null);
    }
  };

  return (
    <div className="flex h-full w-full relative">
      {/* Mobile Sheet */}
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button
            size="icon"
            className="md:hidden absolute top-6 right-4 z-50"
            aria-label="Open menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80 p-4">
          <SheetHeader>
            <SheetTitle>Term Navigation</SheetTitle>
            <SheetDescription>Browse and manage terms</SheetDescription>
          </SheetHeader>
          <TermList
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            terms={filteredTerms}
            selectedTerm={selectedTerm}
            onSelect={handleSelectTerm}
            onTermDeleted={handleTermDeleted}
            isLoading={isLoading}
            isMobile
          />
        </SheetContent>
      </Sheet>

      {/* Sidebar (Desktop) */}
      <TermList
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        terms={filteredTerms}
        selectedTerm={selectedTerm}
        onSelect={handleSelectTerm}
        onTermDeleted={handleTermDeleted}
        isLoading={isLoading}
      />

      {/* Main Content */}
      <div className="flex-1 h-full overflow-hidden">
        <TermDetails selectedTerm={selectedTerm} isLoading={isLoading} />
      </div>
    </div>
  );
}
