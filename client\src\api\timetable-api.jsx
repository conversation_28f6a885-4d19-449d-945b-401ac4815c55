import axiosInstance from "./axios-instance";

export const createTimetable = async (timetableData) => {
  try {
    const response = await axiosInstance.post(
      "/timetables/create",
      timetableData
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error creating timetable:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create timetable");
  }
};

export const getAllTimetables = async () => {
  try {
    const response = await axiosInstance.get("/timetables");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching timetables:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch timetables");
  }
};

export const getTimetableById = async (id) => {
  try {
    const response = await axiosInstance.get(`/timetables/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching timetable ${id}:`,
      error.response?.data || error.message
    );
    throw error.response?.data || new Error(`Failed to fetch timetable ${id}`);
  }
};

export const updateTimetable = async (id, timetableData) => {
  try {
    const response = await axiosInstance.put(
      `/timetables/${id}`,
      timetableData
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error updating timetable ${id}:`,
      error.response?.data || error.message
    );
    throw error.response?.data || new Error(`Failed to update timetable ${id}`);
  }
};

export const deleteTimetable = async (id) => {
  try {
    const response = await axiosInstance.delete(`/timetables/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting timetable ${id}:`,
      error.response?.data || error.message
    );
    throw error.response?.data || new Error(`Failed to delete timetable ${id}`);
  }
};

export const updateTimetableStatus = async (id, status) => {
  try {
    const response = await axiosInstance.patch(`/timetables/${id}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating timetable status ${id}:`,
      error.response?.data || error.message
    );
    throw error.response?.data || new Error(`Failed to update timetable status ${id}`);
  }
};

export const getTimetableByClassSection = async (classId, sectionId) => {
  try {
    const response = await axiosInstance.get(
      `/timetables/class/${classId}/section/${sectionId}`
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching class-section timetable:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch class-section timetable");
  }
};

export const getTeacherTimetable = async (teacherId) => {
  try {
    const response = await axiosInstance.get(`/timetables/teacher/${teacherId}`);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching teacher timetable:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch teacher timetable");
  }
};
