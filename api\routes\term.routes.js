import express from "express";
import {
  createTerm,
  getAllTerms,
  getTermById,
  updateTerm,
  deleteTerm,
  updateTermStatus,
} from "../controllers/term.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createTerm
);

router.get(
  "/",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getAllTerms
);

router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher", "student"]),
  getTermById
);

router.put("/:id", protect, authorize(["admin", "school-admin"]), updateTerm);

router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteTerm
);

router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateTermStatus
);

export default router;
