import { useState, useEffect } from "react";
import { Menu } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetDes<PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useClass } from "@/context/class-context";
import { useSection } from "@/context/section-context";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { ClassList } from "./classes/class-list";
import { SectionList } from "./sections/section-list";

export default function ClassSectionManagement() {
  const [classes, setClasses] = useState([]);
  const [selectedClass, setSelectedClass] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [sections, setSections] = useState([]);

  const { fetchAllClasses } = useClass();
  const { fetchSectionsByClass } = useSection();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await fetchAllClasses();
        setClasses(response.data || []);
      } catch (error) {
        toast.error("Error", {
          description: error.message || "Failed to fetch classes",
        });
        setClasses([]);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    if (selectedClass?._id) {
      setIsLoading(true);
      fetchSectionsByClass(selectedClass._id)
        .then((data) => setSections(data || []))
        .catch((error) => {
          toast.error("Error", {
            description: error.message || "Failed to fetch sections",
          });
          setSections([]);
        })
        .finally(() => setIsLoading(false));
    } else {
      setSections([]);
    }
  }, [selectedClass]);

  const filteredClasses =
    classes?.filter((cls) =>
      cls?.name?.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  const handleSelectClass = (cls) => {
    setSelectedClass(cls);
    setIsSheetOpen(false);
  };

  return (
    <div className="flex h-full w-full relative">
      {/* Mobile Sheet */}
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button
            size="icon"
            className="md:hidden absolute top-6 right-4 z-50"
            aria-label="Open menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80 p-4">
          <SheetHeader>
            <SheetTitle>Class Navigation</SheetTitle>
            <SheetDescription>Browse and manage classes</SheetDescription>
          </SheetHeader>
          <ClassList
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            classes={filteredClasses}
            selectedClass={selectedClass}
            onSelect={handleSelectClass}
            isLoading={isLoading}
            isMobile
          />
        </SheetContent>
      </Sheet>

      {/* Sidebar (Desktop) */}
      <ClassList
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        classes={filteredClasses}
        selectedClass={selectedClass}
        onSelect={handleSelectClass}
        isLoading={isLoading}
      />

      {/* Main Content: Section List for Selected Class */}
      <div className="flex-1 h-full overflow-hidden">
        <SectionList
          selectedClass={selectedClass}
          sections={sections}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
