import { InfoCard } from "@/components/dashboard/info-card";
import { <PERSON>Field } from "@/components/dashboard/data-field";
import {
  Plus,
  Users,
  GraduationCap,
  ClipboardList,
  Info,
  Edit,
  BookOpen,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { ScrollArea } from "@/components/ui/scroll-area";
import { StatCard } from "@/components/dashboard/stat-card";
import { Container } from "@/components/ui/container";
import { PageHeader } from "@/components/dashboard/page-header";
import { Skeleton } from "@/components/ui/skeleton";

export function SectionList({ selectedClass, sections, isLoading }) {
  if (!selectedClass) {
    return (
      <div className="flex flex-col items-center justify-center md:h-[calc(100vh-4rem)] space-y-4">
        <p className="text-muted-foreground">
          {isLoading ? "Loading classes..." : "No class selected"}
        </p>
        {!isLoading && (
          <Button asChild>
            <Link to="/dashboard/academics/classes/create">
              Create a new class
            </Link>
          </Button>
        )}
      </div>
    );
  }

  return (
    <>
      <header className="border-b bg-background">
        <Container className="py-4">
          <PageHeader
            title={`${selectedClass.name}`}
            isLoading={isLoading}
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Academics", href: "/dashboard/academics" },
              {
                label: "Classes",
                href: "/dashboard/academics/classes",
              },
              { label: selectedClass.code },
            ]}
            actions={[
              {
                label: "Add Section",
                icon: Plus,
                href: `/dashboard/academics/sections/create`,
              },
            ]}
          />
        </Container>
      </header>
      <ScrollArea className="h-[calc(100vh-10rem)]">
        <main className="p-4 lg:p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6 mb-6">
            <StatCard
              title="Class Code"
              value={selectedClass.code}
              description={selectedClass.name}
              icon={GraduationCap}
              loading={isLoading}
              valueClassName="uppercase"
              descriptionClassName="text-sm"
            />

            <StatCard
              title="Department"
              value={selectedClass.department?.name || "Not assigned"}
              description={selectedClass.department?.code || "No department"}
              icon={BookOpen}
              loading={isLoading}
              valueClassName="capitalize"
              descriptionClassName="uppercase"
            />

            <StatCard
              title="Sections"
              value={sections?.length || 0}
              description="Total sections"
              icon={Users}
              loading={isLoading}
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <InfoCard
              icon={GraduationCap}
              title="Class Information"
              isLoading={isLoading}
            >
              <div className="space-y-4">
                <DataField
                  label="Class Name"
                  value={selectedClass.name || "Not specified"}
                />
                <DataField
                  label="Class Code"
                  value={selectedClass.code || "Not specified"}
                  className="uppercase"
                />
                <DataField
                  label="Academic Year"
                  value={selectedClass.academicYear || "Not specified"}
                />
                <DataField
                  label="Semester"
                  value={selectedClass.semester || "Not specified"}
                  className="capitalize"
                />
              </div>
            </InfoCard>

            <InfoCard
              icon={ClipboardList}
              title="Class Properties"
              isLoading={isLoading}
            >
              <div className="space-y-4">
                <DataField
                  label="Active Status"
                  value={selectedClass.isActive ? "Yes" : "No"}
                  badge={{
                    variant: selectedClass.isActive ? "default" : "secondary",
                  }}
                />
                <DataField
                  label="Current Status"
                  value={selectedClass.status || "active"}
                  className="capitalize"
                  badge={{
                    variant:
                      selectedClass.status === "active"
                        ? "default"
                        : "secondary",
                  }}
                />
                <DataField
                  label="Class Type"
                  value={selectedClass.type || "Regular"}
                  className="capitalize"
                />
                <DataField
                  label="Maximum Capacity"
                  value={selectedClass.maxCapacity || "Not specified"}
                />
              </div>
            </InfoCard>
          </div>

          <InfoCard
            icon={Users}
            title="Class Sections"
            className="mt-6"
            isLoading={isLoading}
            actions={[
              {
                label: "Add Section",
                icon: Plus,
                href: `/dashboard/academics/classes/${selectedClass._id}/sections/create`,
              },
            ]}
          >
            {isLoading ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="p-4 border rounded-lg">
                    <Skeleton className="h-5 w-1/3 mb-2" />
                    <Skeleton className="h-4 w-1/4 mb-1" />
                    <Skeleton className="h-4 w-1/5" />
                  </div>
                ))}
              </div>
            ) : sections && sections.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {sections.map((section) => (
                  <div
                    key={section._id}
                    className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium text-lg">{section.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          Code: {section.code}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          asChild
                          aria-label="Edit section"
                        >
                          <Link
                            to={`/dashboard/academics/sections/${section._id}/edit`}
                          >
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <DataField
                        label="Status"
                        value={section.status}
                        className="capitalize"
                        badge={{
                          variant:
                            section.status === "active"
                              ? "default"
                              : "secondary",
                        }}
                      />

                      {section.teacher && (
                        <DataField
                          label="Teacher"
                          value={section.teacher.name || "Not assigned"}
                        />
                      )}

                      {section.capacity && (
                        <DataField label="Capacity" value={section.capacity} />
                      )}

                      {section.enrolledCount !== undefined && (
                        <DataField
                          label="Enrolled"
                          value={`${section.enrolledCount}/${
                            section.capacity || "N/A"
                          }`}
                        />
                      )}

                      {section.schedule && (
                        <DataField label="Schedule" value={section.schedule} />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Users className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground mb-4">
                  No sections found for this class
                </p>
                <Button asChild>
                  <Link to={`/dashboard/academics/sections/create`}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Section
                  </Link>
                </Button>
              </div>
            )}
          </InfoCard>

          {!isLoading && selectedClass.description && (
            <InfoCard
              icon={Info}
              title="Description"
              className="mt-6"
              isLoading={isLoading}
            >
              <p className="text-muted-foreground leading-relaxed">
                {selectedClass.description}
              </p>
            </InfoCard>
          )}
        </main>
      </ScrollArea>
    </>
  );
}
