import { useEffect, useState } from "react";
import { useAuth } from "@/context/auth-context";
import { useTimetable } from "@/context/timetable-context";
import { useSubject } from "@/context/subject-context";
import { getCurrentTeacherProfile } from "@/api/teacher-api";
import { PageHeader } from "@/components/dashboard/page-header";
import { Container } from "@/components/ui/container";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import {
  CalendarDays,
  BookOpen,
  GraduationCap,
  RefreshCcwIcon,
} from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { Timetable } from "@/components/timetable/timetable-component/timetable";
import { TimetableSkeleton } from "@/components/timetable/timetable-skeleton/timetable-skeleton";

const TeacherTimetable = () => {
  const { user } = useAuth();
  const { fetchTeacherTimetable, getAggregatedTeacherTimetable } =
    useTimetable();
  const { subjects, fetchAllSubjects } = useSubject();
  const [currentTeacher, setCurrentTeacher] = useState(null);
  const [teacherTimetables, setTeacherTimetables] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [subjectDetails, setSubjectDetails] = useState({});

  useEffect(() => {
    const initializeData = async () => {
      try {
        setIsLoading(true);

        // Get current teacher profile
        const teacherResponse = await getCurrentTeacherProfile();
        const teacher = teacherResponse.data;
        setCurrentTeacher(teacher);

        // Get teacher's timetables
        const timetables = await fetchTeacherTimetable(teacher._id);
        setTeacherTimetables(timetables || []);

        // Fetch subjects for display
        fetchAllSubjects();
      } catch (error) {
        console.error("Error initializing teacher timetable:", error);
        toast.error("Failed to load timetable data", {
          description: error.message || "Please try again later",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (user && user.role === "teacher") {
      initializeData();
    }
  }, [user]);

  useEffect(() => {
    if (subjects && subjects.length > 0) {
      const subjectMap = {};
      subjects.forEach((subject) => {
        subjectMap[subject._id] = {
          name: subject.name,
          code: subject.code,
          color: subject.color || "",
        };
      });
      setSubjectDetails(subjectMap);
    }
  }, [subjects]);

  const getCurrentDaySchedule = () => {
    const aggregatedTimetable = getAggregatedTeacherTimetable(
      teacherTimetables,
      currentTeacher
    );
    if (!aggregatedTimetable) return [];

    const days = [
      "sunday",
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
    ];
    const today = new Date();
    const currentDay = days[today.getDay()];

    return aggregatedTimetable.timetable[currentDay] || [];
  };

  const getTotalWeeklyClasses = () => {
    const aggregatedTimetable = getAggregatedTeacherTimetable(
      teacherTimetables,
      currentTeacher
    );
    if (!aggregatedTimetable) return 0;

    const days = [
      "monday",
      "tuesday",
      "wednesday",
      "thursday",
      "friday",
      "saturday",
    ];
    return days.reduce((total, day) => {
      const dayPeriods = aggregatedTimetable.timetable[day] || [];
      return total + dayPeriods.length;
    }, 0);
  };

  return (
    <Container className="py-8">
      <PageHeader
        title="My Timetable"
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "My Timetable" },
        ]}
        isLoading={isLoading}
        actions={[
          {
            label: "Refresh",
            icon: RefreshCcwIcon,
            onClick: () => window.location.reload(),
          },
        ]}
      />

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 my-6">
        <StatCard
          title="Total Subjects Taught"
          value={subjects.length}
          description={
            getCurrentDaySchedule().length === 0
              ? "No classes scheduled"
              : "Scheduled for today"
          }
          icon={BookOpen}
        />

        <StatCard
          title="Classes Today"
          value={getCurrentDaySchedule().length}
          description="Scheduled for today"
          icon={BookOpen}
        />

        <StatCard
          title="Weekly Classes"
          value={getTotalWeeklyClasses()}
          description="Total classes this week"
          icon={CalendarDays}
        />

        <StatCard
          title="Current Term"
          value={teacherTimetables[0]?.termId?.name || "N/A"}
          description={"Academic Year: " + teacherTimetables[0]?.academicYear}
          icon={GraduationCap}
        />
      </div>

      {/* Timetable Display */}
      <div className="space-y-6">
        {isLoading ? (
          <TimetableSkeleton />
        ) : (
          <>
            {!teacherTimetables || teacherTimetables.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="text-muted-foreground">
                    <CalendarDays className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium mb-2">No Timetable</h3>
                    <p>You don't have any timetables assigned.</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Timetable
                data={getAggregatedTeacherTimetable(
                  teacherTimetables,
                  currentTeacher
                )}
                subjectDetails={subjectDetails}
                isLoading={isLoading}
                mode="teacher"
              />
            )}
          </>
        )}
      </div>
    </Container>
  );
};

export default TeacherTimetable;
