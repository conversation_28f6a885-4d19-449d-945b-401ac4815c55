import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { EventForm } from "@/components/forms/dashboard/schedules/event-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useEvent } from "@/context/event-context";
import { toast } from "sonner";

const CreateEvent = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchEventById, isLoading } = useEvent();
  const [eventData, setEventData] = useState(null);
  const [loading, setLoading] = useState(!!id);

  useEffect(() => {
    const loadEventData = async () => {
      if (id) {
        try {
          const data = await fetchEventById(id);
          setEventData(data);
        } catch (error) {
          console.error("Failed to fetch event data:", error);
          toast.error("Failed to load event data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadEventData();
  }, [id, fetchEventById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Event" : "Create New Event"}
        actions={[
          {
            label: "Back to Events",
            href: "/dashboard/schedules/events",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Schedules", href: "/dashboard/schedules" },
          { label: "Events", href: "/dashboard/schedules/events" },
          { label: id ? "Edit Event" : "Create Event" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <EventForm editingId={id} initialData={eventData} />
      )}
    </Container>
  );
};

export default CreateEvent;
