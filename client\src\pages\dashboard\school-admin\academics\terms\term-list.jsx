import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Calendar, Pencil, Plus, Trash } from "lucide-react";
import { Link } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useTerm } from "@/context/term-context";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";

export function TermList({
  terms = [],
  selectedTerm,
  onSelect,
  isLoading,
  searchQuery = "",
  setSearchQuery,
  isMobile = false,
  onTermDeleted,
}) {
  const { removeTerm } = useTerm();
  const [openAlert, setOpenAlert] = useState(false);
  const [termToDelete, setTermToDelete] = useState(null);

  const handleDelete = async () => {
    if (!termToDelete) return;

    try {
      await removeTerm(termToDelete._id);
      toast.success("Success", {
        description: `Term "${termToDelete.name}" has been deleted successfully.`,
      });
      if (onTermDeleted) {
        onTermDeleted(termToDelete._id);
      }
    } catch (error) {
      console.error("Error deleting term:", error);
      toast.error("Error", {
        description: error.message || "Failed to delete term",
      });
    } finally {
      setOpenAlert(false);
      setTermToDelete(null);
    }
  };

  const confirmDelete = (e, term) => {
    e.stopPropagation();
    setTermToDelete(term);
    setOpenAlert(true);
  };

  return (
    <>
      <div
        className={
          isMobile ? "" : "hidden md:block w-80 border-r bg-background"
        }
      >
        <div className="px-4 h-full flex flex-col">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <h2 className="text-xl font-semibold">Terms</h2>
            </div>
            <Button
              variant="ghost"
              size="icon"
              asChild
              aria-label="Add new term"
            >
              <Link to="/dashboard/academics/terms/create">
                <Plus className="h-5 w-5" />
              </Link>
            </Button>
          </div>
          <Input
            type="search"
            placeholder="Search terms..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="my-3"
            aria-label="Search terms"
          />
          <ScrollArea className="flex-1">
            {isLoading ? (
              <div className="space-y-2">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="p-3 space-y-2">
                    <Skeleton className="h-5 w-3/4" />
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-4 w-1/4" />
                    </div>
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                ))}
              </div>
            ) : terms.length > 0 ? (
              <ul className="space-y-1">
                {terms.map((term) => (
                  <li key={term._id}>
                    <div className="relative group">
                      <button
                        className={`block w-full text-left p-3 rounded-lg transition ${
                          selectedTerm?._id === term._id
                            ? "bg-primary/10"
                            : "hover:bg-muted"
                        }`}
                        onClick={() => onSelect(term)}
                        aria-label={`Select ${term.name}`}
                      >
                        <div className="flex-1 min-w-0">
                          <h3 className="text-base font-medium truncate">
                            {term.name}
                          </h3>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                            <Calendar className="w-3 h-3 mr-1" />
                            <span>Code:</span>
                            {term.code && (
                              <span className="px-1.5 py-0.5 bg-secondary/20 rounded text-secondary-foreground">
                                {term.code}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-2 mt-2">
                            {term.academicYear && (
                              <Badge variant="outline" className="text-xs">
                                {term.academicYear}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </button>
                      <div className="absolute top-2 right-2 hidden group-hover:flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          asChild
                          aria-label="Edit term"
                        >
                          <Link
                            to={`/dashboard/academics/terms/${term._id}/edit`}
                          >
                            <Pencil className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => confirmDelete(e, term)}
                          aria-label="Delete term"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="flex justify-center p-4">No terms found</div>
            )}
          </ScrollArea>
        </div>
      </div>

      <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {termToDelete?.name}?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              term and remove its data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Term
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
