import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useTeacher } from "@/context/teacher-context";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/utils/date-filters";
import {
  User,
  ArrowLeft,
  Edit,
  Trash2,
  MapPin,
  Briefcase,
  FileText,
  Users,
  Settings,
  Phone,
  Mail,
  Building,
  GraduationCap,
  Heart,
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  BookOpen,
  AlertTriangle,
  Award,
  UserCheck,
  Zap,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { InfoCard } from "@/components/dashboard/info-card";
import { DataField } from "@/components/dashboard/data-field";
import { ProfileBanner } from "@/components/dashboard/profile-banner";
import { ViewDetailSkeleton } from "@/components/dashboard/view-detail-skeleton";
import { useDepartment } from "@/context/department-context";
import { useSubject } from "@/context/subject-context";

const ViewTeacher = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchTeacherById, removeTeacher, updateStatus } = useTeacher();
  const { fetchDepartmentById } = useDepartment();
  const { fetchSubjectById } = useSubject();
  const [teacher, setTeacher] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [openDelete, setOpenDelete] = useState(false);
  const [openStatusChange, setOpenStatusChange] = useState(false);

  useEffect(() => {
    const fetchTeacher = async () => {
      try {
        setIsLoading(true);
        const data = await fetchTeacherById(id);
        setTeacher(data);
      } catch (error) {
        console.error("Failed to fetch teacher:", error);
        toast.error("Error", {
          description: error.message || "Failed to load teacher details",
        });
        navigate("/dashboard/teachers");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchTeacher();
    } else {
      console.error("No teacher ID provided");
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    const fetchDepartmentName = async () => {
      if (teacher?.department) {
        try {
          const response = await fetchDepartmentById(teacher.department);
          if (response) {
            setTeacher((prevTeacher) => ({
              ...prevTeacher,
              department: {
                _id: response._id,
                name: response.name,
              },
            }));
          }
        } catch (error) {
          console.error("Failed to fetch department name:", error);
        }
      }
    };

    fetchDepartmentName();
  }, [teacher?.department]);

  useEffect(() => {
    const fetchSubjectName = async () => {
      if (teacher?.primarySubject) {
        try {
          const response = await fetchSubjectById(teacher.primarySubject);
          if (response) {
            setTeacher((prevTeacher) => ({
              ...prevTeacher,
              primarySubject: {
                _id: response._id,
                name: response.name,
              },
            }));
          }
        } catch (error) {
          console.error("Failed to fetch primary subject name:", error);
        }
        if (teacher?.secondarySubject) {
          try {
            const response = await fetchSubjectById(teacher.secondarySubject);
            if (response) {
              setTeacher((prevTeacher) => ({
                ...prevTeacher,
                secondarySubject: {
                  _id: response._id,
                  name: response.name,
                },
              }));
            }
          } catch (error) {
            console.error("Failed to fetch secondary subject name:", error);
          }
        }
      }
    };

    fetchSubjectName();
  }, [teacher?.primarySubject]);

  const handleDelete = async () => {
    try {
      await removeTeacher(teacher._id);
      toast.success("Teacher deleted successfully", {
        description: `${teacher.firstName} ${teacher.lastName} has been removed.`,
      });
      navigate("/dashboard/teachers");
    } catch (error) {
      console.error("Failed to delete teacher:", error);
      toast.error("Delete Failed", {
        description: error.message || "Failed to delete teacher",
      });
    }
  };

  const handleStatusChange = async () => {
    try {
      const newStatus = teacher.status === "active" ? "inactive" : "active";
      await updateStatus(teacher._id, { status: newStatus });
      setOpenStatusChange(false);
      setTeacher((prevTeacher) => ({
        ...prevTeacher,
        status: newStatus,
      }));
      toast.success("Status updated successfully", {
        description: `${teacher.firstName} ${teacher.lastName}'s status has been changed to ${newStatus}.`,
      });
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Update Failed", {
        description: error.message || "Failed to update teacher status",
      });
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "inactive":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "on-leave":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "active":
        return <CheckCircle className="h-3 w-3 mr-1" />;
      case "inactive":
        return <XCircle className="h-3 w-3 mr-1" />;
      case "on-leave":
        return <AlertTriangle className="h-3 w-3 mr-1" />;
      default:
        return <XCircle className="h-3 w-3 mr-1" />;
    }
  };

  if (isLoading) {
    return <ViewDetailSkeleton />;
  }

  return (
    <Container className="py-8">
      <div className="space-y-6">
        <PageHeader
          title="Teacher Profile"
          isLoading={isLoading}
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Teachers", href: "/dashboard/teachers" },
            { label: `${teacher.firstName} ${teacher.lastName}` },
          ]}
          actions={[
            {
              label: "Edit Profile",
              icon: Edit,
              href: `/dashboard/teachers/${teacher._id}/edit`,
            },
            {
              label: "Back to Teachers",
              icon: ArrowLeft,
              href: "/dashboard/teachers",
              variant: "outline",
            },
          ]}
        />

        <ProfileBanner
          data={teacher}
          loading={isLoading}
          setOpenStatusChange={setOpenStatusChange}
          setOpenDelete={setOpenDelete}
        />

        <div className="grid grid-cols-1 lg:grid-cols-3 lg:gap-4">
          <div className="md:col-span-1">
            <div className="grid grid-cols-1 gap-6">
              <InfoCard
                icon={User}
                title={`${teacher.title} ${teacher.firstName} ${teacher.lastName}`}
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="Employee ID"
                    value={teacher.employeeId}
                    copyable
                  />
                  <DataField
                    label="System Email"
                    value={teacher.email}
                    icon={Mail}
                    copyable
                  />
                  <DataField
                    label="Personal Email"
                    value={teacher.personalEmail}
                    icon={Mail}
                    copyable
                  />
                  <DataField
                    label="Phone"
                    value={teacher.phone}
                    icon={Phone}
                    copyable
                  />
                  {teacher.alternatePhone && (
                    <DataField
                      label="Alternate Phone"
                      value={teacher.alternatePhone}
                      icon={Phone}
                      copyable
                    />
                  )}
                  <DataField
                    label="Preferred Contact"
                    value={
                      teacher.preferredContactMethod?.charAt(0).toUpperCase() +
                      teacher.preferredContactMethod?.slice(1)
                    }
                  />
                </div>
              </InfoCard>

              <InfoCard
                icon={Shield}
                title="Identity Documents"
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="Aadhar Number"
                    value={teacher.aadharNumber}
                    copyable
                  />
                  <DataField
                    label="PAN Number"
                    value={teacher.panNumber}
                    copyable
                  />
                  {teacher.profilePhoto && (
                    <DataField
                      label="Profile Photo"
                      value={teacher.profilePhoto}
                    />
                  )}
                </div>
              </InfoCard>

              <InfoCard
                icon={Shield}
                title="System Access"
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="System Email"
                    value={teacher.email}
                    icon={Mail}
                    copyable
                  />
                  <DataField
                    label="Account Status"
                    value={teacher.isActive ? "Active" : "Inactive"}
                  />
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                      Status
                    </label>
                    <Badge
                      variant="outline"
                      className={`w-fit ${getStatusColor(teacher.status)}`}
                    >
                      {getStatusIcon(teacher.status)}
                      {teacher.status?.charAt(0).toUpperCase() +
                        teacher.status?.slice(1)}
                    </Badge>
                  </div>
                </div>
              </InfoCard>

              <InfoCard
                icon={FileText}
                title="Additional Notes"
                isLoading={isLoading}
              >
                <div className="bg-muted/50 rounded-lg p-4 min-h-[120px]">
                  <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                    {teacher.notes ||
                      teacher.additionalNotes ||
                      "No additional notes available."}
                  </p>
                </div>
              </InfoCard>
            </div>
          </div>

          <div className="md:col-span-2 rounded-xl">
            <Tabs defaultValue="personal" className="w-full">
              <div className="mb-4">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="personal">
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline">Personal</span>
                  </TabsTrigger>
                  <TabsTrigger value="professional">
                    <Briefcase className="h-4 w-4" />
                    <span className="hidden sm:inline">Professional</span>
                  </TabsTrigger>
                  <TabsTrigger value="classes">
                    <BookOpen className="h-4 w-4" />
                    <span className="hidden sm:inline">Classes</span>
                    {teacher.assignedClasses?.length > 0 && (
                      <Badge
                        variant="secondary"
                        className="ml-1 h-5 w-5 p-0 text-xs"
                      >
                        {teacher.assignedClasses.length}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="additional">
                    <FileText className="h-4 w-4" />
                    <span className="hidden sm:inline">Additional</span>
                  </TabsTrigger>
                  <TabsTrigger value="system">
                    <Settings className="h-4 w-4" />
                    <span className="hidden sm:inline">System</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Personal Information Tab */}
              <TabsContent value="personal" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={User}
                      title="Personal Details"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <DataField label="Title" value={teacher.title} />
                        <DataField
                          label="Employee ID"
                          value={teacher.employeeId}
                        />
                        <DataField label="Gender" value={teacher.gender} />
                        <DataField
                          label="Date of Birth"
                          value={
                            teacher.dateOfBirth
                              ? formatDate(teacher.dateOfBirth)
                              : null
                          }
                        />
                        <DataField
                          label="Marital Status"
                          value={teacher.maritalStatus}
                        />
                        <DataField
                          label="Blood Group"
                          value={teacher.bloodGroup}
                          icon={Heart}
                        />
                        <DataField
                          label="Nationality"
                          value={teacher.nationality}
                        />
                        <DataField label="Religion" value={teacher.religion} />
                      </div>
                    </InfoCard>
                  </div>

                  <InfoCard
                    icon={Phone}
                    title="Contact Information"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Personal Email"
                        value={teacher.personalEmail}
                        icon={Mail}
                        copyable
                      />
                      <DataField
                        label="Phone Number"
                        value={teacher.phone}
                        icon={Phone}
                        copyable
                      />
                      {teacher.alternatePhone && (
                        <DataField
                          label="Alternate Phone"
                          value={teacher.alternatePhone}
                          icon={Phone}
                          copyable
                        />
                      )}
                      <DataField
                        label="Preferred Contact Method"
                        value={teacher.preferredContactMethod}
                      />
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={Shield}
                    title="Identity Documents"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Aadhar Number"
                        value={teacher.aadharNumber}
                        copyable
                      />
                      <DataField
                        label="PAN Number"
                        value={teacher.panNumber}
                        copyable
                      />
                      {teacher.profilePhoto && (
                        <DataField
                          label="Profile Photo"
                          value={teacher.profilePhoto}
                        />
                      )}
                    </div>
                  </InfoCard>

                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={AlertTriangle}
                      title="Emergency Contact Information"
                      isLoading={isLoading}
                    >
                      <div className="grid grid-cols-3 space-y-4">
                        <DataField
                          label="Contact Name"
                          value={teacher.emergencyContactName}
                          icon={UserCheck}
                        />
                        <DataField
                          label="Relationship"
                          value={teacher.emergencyContactRelation}
                        />
                        <DataField
                          label="Contact Phone"
                          value={teacher.emergencyContactPhone}
                          icon={Phone}
                          copyable
                        />
                      </div>
                    </InfoCard>
                  </div>

                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={MapPin}
                      title="Address Information"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <div className="space-y-4">
                        <DataField label="Address" value={teacher.address} />
                        <div className="grid grid-cols-2 gap-4">
                          <DataField label="City" value={teacher.city} />
                          <DataField label="State" value={teacher.state} />
                          <DataField
                            label="Pincode"
                            value={teacher.pincode}
                            copyable
                          />
                          <DataField label="Country" value={teacher.country} />
                        </div>
                      </div>
                    </InfoCard>
                  </div>
                </div>
              </TabsContent>

              {/* Professional Information Tab */}
              <TabsContent value="professional" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InfoCard
                    icon={Briefcase}
                    title="Employment Details"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Designation"
                        value={teacher.designation}
                      />
                      <DataField
                        label="Employment Type"
                        value={teacher.employmentType}
                      />
                      <DataField
                        label="Department"
                        value={teacher.department.name || teacher.department}
                      />
                      <DataField
                        label="Experience"
                        value={teacher.experience}
                      />
                      <DataField
                        label="Previous Experience"
                        value={teacher.previousExperience}
                      />
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={BookOpen}
                    title="Subjects & Qualification"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Primary Subject"
                        value={
                          teacher.primarySubject?.name || teacher.primarySubject
                        }
                      />
                      <DataField
                        label="Secondary Subject"
                        value={
                          teacher.secondarySubject?.name ||
                          teacher.secondarySubject
                        }
                      />
                      <DataField
                        label="Qualification"
                        value={teacher.qualification}
                        icon={GraduationCap}
                      />
                      <DataField
                        label="Specializations"
                        value={teacher.specializations}
                      />
                    </div>
                  </InfoCard>

                  {(teacher.bankName ||
                    teacher.bankAccountNumber ||
                    teacher.ifscCode) && (
                    <InfoCard
                      icon={Building}
                      title="Banking Information"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <DataField label="Bank Name" value={teacher.bankName} />
                        <DataField
                          label="Account Number"
                          value={teacher.bankAccountNumber}
                          copyable
                        />
                        <DataField
                          label="IFSC Code"
                          value={teacher.ifscCode}
                          copyable
                        />
                      </div>
                    </InfoCard>
                  )}

                  {teacher.achievements && (
                    <InfoCard
                      icon={Award}
                      title="Achievements"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <div className="bg-muted/50 rounded-lg p-4">
                        <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                          {teacher.achievements}
                        </p>
                      </div>
                    </InfoCard>
                  )}
                </div>
              </TabsContent>

              {/* Assigned Classes Tab */}
              <TabsContent value="classes" className="space-y-6">
                <InfoCard
                  icon={BookOpen}
                  title={`Assigned Classes (${
                    teacher.assignedClasses?.length || 0
                  })`}
                  isLoading={isLoading}
                >
                  {teacher.assignedClasses &&
                  teacher.assignedClasses.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                      {teacher.assignedClasses.map((classItem, index) => (
                        <Card
                          key={classItem._id || index}
                          className="border hover:border-primary/50 transition-colors"
                        >
                          <CardContent className="p-4">
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-12 w-12">
                                <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                                  {classItem.name?.charAt(0) ||
                                    classItem.className?.charAt(0) ||
                                    "C"}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <p className="font-semibold text-foreground truncate">
                                  {classItem.name ||
                                    classItem.className ||
                                    "Class name not available"}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {classItem.section || "Section not available"}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted mb-4">
                        <BookOpen className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium text-foreground mb-2">
                        No classes assigned
                      </h3>
                      <p className="text-muted-foreground">
                        This teacher has no classes assigned yet.
                      </p>
                    </div>
                  )}
                </InfoCard>
              </TabsContent>

              {/* Additional Information Tab */}
              <TabsContent value="additional" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InfoCard
                    icon={Shield}
                    title="System Access"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="System Email"
                        value={teacher.email}
                        icon={Mail}
                        copyable
                      />
                      <DataField
                        label="Account Status"
                        value={teacher.isActive ? "Active" : "Inactive"}
                      />
                      <div className="space-y-1">
                        <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                          Status
                        </label>
                        <Badge
                          variant="outline"
                          className={`w-fit ${getStatusColor(teacher.status)}`}
                        >
                          {getStatusIcon(teacher.status)}
                          {teacher.status?.charAt(0).toUpperCase() +
                            teacher.status?.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={FileText}
                    title="Additional Notes"
                    isLoading={isLoading}
                  >
                    <div className="bg-muted/50 rounded-lg p-4 min-h-[120px]">
                      <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                        {teacher.notes ||
                          teacher.additionalNotes ||
                          "No additional notes available."}
                      </p>
                    </div>
                  </InfoCard>
                </div>
              </TabsContent>

              {/* System Information Tab */}
              <TabsContent value="system" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InfoCard
                    icon={Clock}
                    title="Timestamps"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Created At"
                        value={formatDate(teacher.createdAt)}
                      />
                      <DataField
                        label="Last Updated"
                        value={formatDate(teacher.updatedAt)}
                      />
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={Settings}
                    title="System Details"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Teacher ID"
                        value={teacher._id}
                        copyable
                      />
                      <DataField
                        label="Employee ID"
                        value={teacher.employeeId}
                        copyable
                      />
                      <DataField
                        label="School ID"
                        value={teacher.schoolId?._id || teacher.schoolId}
                        copyable
                      />
                      <div className="space-y-1">
                        <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                          Status
                        </label>
                        <Badge
                          variant="outline"
                          className={`w-fit ${getStatusColor(teacher.status)}`}
                        >
                          {getStatusIcon(teacher.status)}
                          {teacher.status?.charAt(0).toUpperCase() +
                            teacher.status?.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  </InfoCard>

                  {teacher.assignedClasses?.length > 0 && (
                    <InfoCard
                      icon={BookOpen}
                      title="Classes Summary"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <div className="space-y-2">
                        <DataField
                          label="Total Classes"
                          value={teacher.assignedClasses.length.toString()}
                        />
                        <div className="text-sm text-muted-foreground">
                          Class IDs:{" "}
                          {teacher.assignedClasses
                            .map(
                              (classItem) =>
                                classItem._id ||
                                (typeof classItem === "object"
                                  ? classItem.name
                                  : classItem)
                            )
                            .join(", ")}
                        </div>
                      </div>
                    </InfoCard>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-destructive" />
              Delete {teacher.firstName} {teacher.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              teacher's profile and remove all associated data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive hover:bg-destructive/90"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Teacher
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Status Change Confirmation Dialog */}
      <AlertDialog open={openStatusChange} onOpenChange={setOpenStatusChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              {teacher.status === "active" ? (
                <XCircle className="h-5 w-5 text-muted-foreground" />
              ) : (
                <CheckCircle className="h-5 w-5 text-primary" />
              )}
              {teacher.status === "active" ? "Deactivate" : "Activate"}{" "}
              {teacher.firstName} {teacher.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to{" "}
              {teacher.status === "active" ? "deactivate" : "activate"} this
              teacher's account?
              {teacher.status === "active"
                ? " They will lose access to the system until reactivated."
                : " They will regain full access to the system."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleStatusChange}>
              {teacher.status === "active" ? "Deactivate" : "Activate"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Container>
  );
};

export default ViewTeacher;
