import { useEffect, useState } from "react";
import { useEvent } from "@/context/event-context";
import { Calendar, CalendarDays, Clock, Users, PlusCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { EventCard } from "@/pages/dashboard/school-admin/schedules/events/event-card";
import { DataCardSearchbar } from "@/components/data-card/data-card-component/data-card-searchbar";
import { DataCardsSkeleton } from "@/components/data-card/data-card-skeleton/data-cards-skeleton";

const EventDirectory = () => {
  const {
    events,
    isLoading,
    fetchAllEvents,
    removeEvent,
    updateStatus,
    getEventsByStatus,
    getTodaysEvents,
    getThisWeeksEvents,
  } = useEvent();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterType, setFilterType] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");

  useEffect(() => {
    fetchAllEvents();
  }, []);

  const filteredEvents = events.filter((event) => {
    const matchesSearch =
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.venueName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      filterStatus === "all" || event.status === filterStatus;
    const matchesType = filterType === "all" || event.eventType === filterType;
    const matchesPriority =
      filterPriority === "all" || event.priority === filterPriority;

    return matchesSearch && matchesStatus && matchesType && matchesPriority;
  });

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Event Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Schedule", href: "/dashboard/schedules" },
            { label: "Events" },
          ]}
          actions={[
            {
              label: "Create Event",
              icon: PlusCircle,
              href: "/dashboard/schedules/events/create",
            },
          ]}
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Events"
            value={events.length}
            description="All events"
            icon={Calendar}
            isLoading={isLoading}
            trend="positive"
          />
          <StatCard
            title="Scheduled Events"
            value={
              events.filter((event) => event.status === "scheduled").length
            }
            description="Upcoming events"
            icon={CalendarDays}
            isLoading={isLoading}
            trend="positive"
          />
          <StatCard
            title="Today's Events"
            value={getTodaysEvents().length}
            description="Events today"
            icon={Clock}
            isLoading={isLoading}
          />
          <StatCard
            title="This Week"
            value={getThisWeeksEvents().length}
            description="Events this week"
            icon={Users}
            isLoading={isLoading}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Filters Sidebar */}
          <div className="">
            <Card>
              <CardHeader className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                <h3 className="text-2xl font-bold">Filters</h3>
              </CardHeader>
              <CardContent>
                <DataCardSearchbar
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  placeholder="Search events..."
                  filterOptions={[
                    {
                      value: filterStatus,
                      onChange: setFilterStatus,
                      placeholder: "Status",
                      options: [
                        { value: "all", label: "All Status" },
                        { value: "scheduled", label: "Scheduled" },
                        { value: "in_progress", label: "In Progress" },
                        { value: "completed", label: "Completed" },
                        { value: "cancelled", label: "Cancelled" },
                        { value: "postponed", label: "Postponed" },
                      ],
                    },
                    {
                      value: filterType,
                      onChange: setFilterType,
                      placeholder: "Type",
                      options: [
                        { value: "all", label: "All Types" },
                        { value: "academic", label: "Academic" },
                        { value: "sports", label: "Sports" },
                        { value: "cultural", label: "Cultural" },
                        { value: "competition", label: "Competition" },
                        { value: "meeting", label: "Meeting" },
                        { value: "workshop", label: "Workshop" },
                        { value: "exam", label: "Exam" },
                        { value: "other", label: "Other" },
                      ],
                    },
                    {
                      value: filterPriority,
                      onChange: setFilterPriority,
                      placeholder: "Priority",
                      options: [
                        { value: "all", label: "All Priority" },
                        { value: "low", label: "Low" },
                        { value: "medium", label: "Medium" },
                        { value: "high", label: "High" },
                        { value: "urgent", label: "Urgent" },
                      ],
                    },
                  ]}
                />
              </CardContent>
            </Card>
          </div>
          {/* Events Area */}
          <div className="col-span-2">
            <div className="space-y-4">
              {isLoading ? (
                <DataCardsSkeleton />
              ) : (
                <EventCards
                  events={filteredEvents}
                  onEdit={(id) =>
                    navigate(`/dashboard/schedules/events/${id}/edit`)
                  }
                  onView={(id) => navigate(`/dashboard/schedules/events/${id}`)}
                  onDelete={removeEvent}
                  onStatusChange={updateStatus}
                />
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

// Event Cards Component
const EventCards = ({ events, onEdit, onView, onDelete, onStatusChange }) => {
  if (events.length === 0) {
    return (
      <div className="text-center py-12">
        <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No events found</h3>
        <p className="mt-2 text-muted-foreground">
          Get started by creating your first event.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {events.map((event) => (
        <EventCard
          key={event._id}
          event={event}
          onEdit={onEdit}
          onView={onView}
          onDelete={onDelete}
          onStatusChange={onStatusChange}
        />
      ))}
    </div>
  );
};

export default EventDirectory;
