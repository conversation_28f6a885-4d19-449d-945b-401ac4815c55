import React, { useState, useEffect } from "react";
import { Container } from "@/components/ui/container";
import { TermForm } from "@/components/forms/dashboard/academics/terms/term-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useTerm } from "@/context/term-context";
import { toast } from "sonner";

const CreateTerms = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(!!id);
  const [termData, setTermData] = useState(null);
  const { fetchTermById } = useTerm();

  useEffect(() => {
    if (id) {
      const fetchData = async () => {
        try {
          const data = await fetchTermById(id);
          setTermData(data);
          setLoading(false);
        } catch (error) {
          console.error("Error fetching term:", error);
          toast.error("Failed to load term data");
          navigate("/dashboard/academics/terms");
        }
      };

      fetchData();
    }
  }, [id, fetchTermById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Term" : "Create New Term"}
        actions={[
          {
            label: "Back to Terms",
            href: "/dashboard/academics/terms",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Academics", href: "/dashboard/academics" },
          { label: "Terms", href: "/dashboard/academics/terms" },
          { label: id ? "Edit Term" : "Create Term" },
        ]}
      />

      {id && loading ? (
        <FormCardSkeleton />
      ) : (
        <TermForm editingId={id} initialData={termData} />
      )}
    </Container>
  );
};

export default CreateTerms;
