import { useEffect } from "react";
import { useTeacher } from "@/context/teacher-context";
import {
  <PERSON>,
  <PERSON>r<PERSON><PERSON><PERSON>,
  PlusCircle,
  CalendarClock,
  GraduationCap,
} from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { TeacherColumns } from "@/pages/dashboard/school-admin/teachers/teacher-columns";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";

const TeacherDirectory = () => {
  const { teachers, isLoading, fetchAllTeachers } = useTeacher();

  useEffect(() => {
    fetchAllTeachers();
  }, []);

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Teacher Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Teachers" },
          ]}
          actions={[
            {
              label: "New Teacher",
              icon: PlusCircle,
              href: "/dashboard/teachers/create",
            },
          ]}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Teachers"
            value={teachers.length}
            description="All registered teachers"
            icon={Users}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Active Teachers"
            value={teachers.filter((t) => t.status === "active").length}
            description="Currently teaching"
            icon={UserCheck}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Recently Joined"
            value={
              teachers.filter((teacher) => {
                const createdAt = new Date(teacher.createdAt);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return createdAt >= thirtyDaysAgo;
              }).length
            }
            description="Joined in last 30 days"
            icon={CalendarClock}
            isLoading={isLoading}
          />

          <StatCard
            title="Top Empl Type"
            value={(() => {
              if (teachers.length === 0) return "N/A";
              const typeCount = teachers.reduce((acc, teacher) => {
                const type = teacher.employmentType || "Unknown";
                acc[type] = (acc[type] || 0) + 1;
                return acc;
              }, {});
              let topType = "Unknown";
              let max = 0;
              for (const [type, count] of Object.entries(typeCount)) {
                if (count > max) {
                  topType = type;
                  max = count;
                }
              }
              return topType;
            })()}
            description="Most teachers are"
            icon={GraduationCap}
            isLoading={isLoading}
          />
        </div>

        <div>
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={teachers}
              columns={TeacherColumns()}
              model="teacher"
            />
          )}
        </div>
      </main>
    </div>
  );
};

export default TeacherDirectory;
