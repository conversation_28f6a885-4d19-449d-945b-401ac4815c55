import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { TeacherForm } from "@/components/forms/dashboard/teachers/teacher-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useTeacher } from "@/context/teacher-context";
import { toast } from "sonner";

const CreateTeachers = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchTeacherById } = useTeacher();
  const [teacherData, setTeacherData] = useState(null);
  const [loading, setLoading] = useState(!!id);

  useEffect(() => {
    const loadTeacherData = async () => {
      if (id) {
        try {
          const data = await fetchTeacherById(id);
          setTeacherData(data);
        } catch (error) {
          console.error("Failed to fetch teacher data:", error);
          toast.error("Failed to load teacher data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadTeacherData();
  }, [id, fetchTeacherById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Teacher" : "Create New Teacher"}
        actions={[
          {
            label: "Back to Teachers",
            href: "/dashboard/teachers",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Teachers", href: "/dashboard/teachers" },
          { label: id ? "Edit Teacher" : "Create Teacher" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <TeacherForm editingId={id} initialData={teacherData} />
      )}
    </Container>
  );
};

export default CreateTeachers;
