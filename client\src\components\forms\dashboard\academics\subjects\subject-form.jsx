import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNavigate } from "react-router-dom";
import { Book, FileText, GraduationCap } from "lucide-react";
import { academicYears, subjectTypes } from "@/utils/form-options";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import { useDepartment } from "@/context/department-context";
import { useSubject } from "@/context/subject-context";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import { SwitchInput } from "@/components/form-inputs/switch-input";

export function SubjectForm({ editingId, initialData }) {
  const navigate = useNavigate();
  const { fetchAllDepartments, departmentOptions } = useDepartment();
  const { addSubject, editSubject } = useSubject();

  const form = useForm({
    defaultValues: {
      // Basic Information
      name: initialData?.name || "",
      code: initialData?.code || "",
      type: initialData?.type || "",
      department: initialData?.department || "",
      description: initialData?.description || "",

      // Academic Details
      credit: initialData?.credit || "",
      semester: initialData?.semester || "",
      academicYear: initialData?.academicYear || "",
      totalMarks: initialData?.totalMarks || "",
      passMarks: initialData?.passMarks || "",
      prerequisites: initialData?.prerequisites || "",

      // Other details
      hasLabs: initialData?.hasLabs || false,
      hasPracticalExams: initialData?.hasPracticalExams || false,
      hasTheoryExams: initialData?.hasTheoryExams || false,
      hasAssignments: initialData?.hasAssignments || false,
      isElective: initialData?.isElective || false,
      isActive: initialData?.isActive || false,

      // Additional Information
      notes: initialData?.notes || "",
    },
  });

  useEffect(() => {
    fetchAllDepartments();
  }, [fetchAllDepartments]);

  const handleSubmit = async (data) => {
    try {
      if (editingId) {
        await editSubject(editingId, data);
        toast.success("Subject updated successfully.", {
          description: `${data.name} has been updated.`,
        });
        navigate("/dashboard/academics/subjects");
      } else {
        await addSubject(data);
        toast.success("Subject created successfully.", {
          description: `${data.name} has been added to the subject list.`,
        });
        navigate("/dashboard/academics/subjects");
      }
    } catch (error) {
      console.error("Subject form error:", error);
      toast.error(
        error.response?.data?.message || error.message || "Submission failed."
      );
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormCard title="Subject Information" icon={Book}>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <TextInput
                form={form}
                name="name"
                label="Subject Name"
                placeholder="e.g., Data Structures"
                validation={{ required: "Subject name is required" }}
              />
              <TextInput
                form={form}
                name="code"
                label="Subject Code"
                placeholder="e.g., CS201"
                validation={{ required: "Subject code is required" }}
              />
              <SelectInput
                form={form}
                name="type"
                label="Subject Type"
                placeholder="Select type"
                options={subjectTypes}
                validation={{ required: "Subject type is required" }}
              />
              <ComboboxInput
                form={form}
                name="department"
                label="Department"
                options={departmentOptions}
                placeholder="Select department"
                validation={{ required: "Department is required" }}
                href="/dashboard/academics/departments"
                toolTipText="Add new Department"
              />
              <div className="lg:col-span-2">
                <TextareaInput
                  form={form}
                  name="description"
                  label="Description"
                  placeholder="Brief description of the subject..."
                  validation={{
                    minLength: {
                      value: 10,
                      message: "Description must be at least 10 characters",
                    },
                    maxLength: {
                      value: 500,
                      message: "Description must not exceed 500 characters",
                    },
                  }}
                  inputProps={{ rows: 4 }}
                />
              </div>
            </div>
          </FormCard>

          <FormCard title="Academic Details" icon={GraduationCap}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
              <TextInput
                form={form}
                name="credit"
                label="Credit Hours"
                type="number"
                placeholder="Enter credit hours"
                validation={{
                  required: "Credit hours is required",
                  min: { value: 1, message: "Credit hours must be at least 1" },
                  max: { value: 10, message: "Credit hours cannot exceed 10" },
                }}
              />
              <TextInput
                form={form}
                name="semester"
                label="Semester"
                type="number"
                placeholder="Enter semester"
                validation={{
                  required: "Semester is required",
                  min: { value: 1, message: "Semester must be at least 1" },
                  max: { value: 8, message: "Semester cannot exceed 8" },
                }}
              />{" "}
              <SelectInput
                form={form}
                name="academicYear"
                label="Academic Year"
                options={academicYears}
                placeholder="Select academic year"
                validation={{ required: "Academic year is required" }}
              />
              <div className="md:col-span-3">
                <div className="grid grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="totalMarks"
                    label="Total Marks"
                    type="number"
                    placeholder="Enter total marks"
                    validation={{
                      required: "Total marks is required",
                      min: {
                        value: 1,
                        message: "Total marks must be at least 1",
                      },
                      max: {
                        value: 1000,
                        message: "Total marks cannot exceed 1000",
                      },
                    }}
                  />
                  <TextInput
                    form={form}
                    name="passMarks"
                    label="Pass Marks"
                    type="number"
                    placeholder="Enter pass marks"
                    validation={{
                      required: "Pass marks is required",
                      min: {
                        value: 1,
                        message: "Pass marks must be at least 1",
                      },
                      validate: (value) => {
                        const totalMarks = form.getValues("totalMarks");
                        if (
                          totalMarks &&
                          parseInt(value) >= parseInt(totalMarks)
                        ) {
                          return "Pass marks must be less than total marks";
                        }
                        return true;
                      },
                    }}
                  />
                </div>
              </div>
              <div className="lg:col-span-3 mb-6">
                <TextareaInput
                  form={form}
                  name="prerequisites"
                  label="Prerequisites"
                  placeholder="List any prerequisite subjects or requirements..."
                  inputProps={{ rows: 3 }}
                />
              </div>
            </div>

            <FormCard title="Other Details" icon={Book}>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <SwitchInput form={form} label="Has Labs" name="hasLabs" />
                <SwitchInput
                  form={form}
                  label="Has Practical Exams"
                  name="hasPracticalExams"
                />
                <SwitchInput
                  form={form}
                  label="Has Theory Exams"
                  name="hasTheoryExams"
                />
                <SwitchInput
                  form={form}
                  label="Has Assignments"
                  name="hasAssignments"
                />
                <SwitchInput
                  form={form}
                  label="Is Elective"
                  name="isElective"
                />
                <SwitchInput form={form} name="isActive" label="Is Active" />
              </div>
            </FormCard>
          </FormCard>

          <FormCard title="Additional Information" icon={FileText}>
            <div className="grid grid-cols-1 gap-4">
              <TextareaInput
                form={form}
                name="notes"
                label="Notes"
                placeholder="Enter additional notes about this subject"
                inputProps={{ rows: 3 }}
              />
            </div>
          </FormCard>

          <FormFooter
            href="/dashboard/academics/subjects"
            editingId={editingId}
            title="Subject"
          />
        </form>
      </Form>
    </div>
  );
}
