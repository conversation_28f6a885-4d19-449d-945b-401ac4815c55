import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Book, Pencil, Plus, Trash, GraduationCap } from "lucide-react";
import { Link } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useSubject } from "@/context/subject-context";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";

export function SubjectList({
  subjects = [],
  selectedSubject,
  onSelect,
  isLoading,
  searchQuery = "",
  setSearchQuery,
  isMobile = false,
  onSubjectDeleted,
}) {
  const { removeSubject } = useSubject();
  const [openAlert, setOpenAlert] = useState(false);
  const [subjectToDelete, setSubjectToDelete] = useState(null);

  const handleDelete = async () => {
    if (!subjectToDelete) return;

    try {
      await removeSubject(subjectToDelete._id);
      toast.success("Success", {
        description: `Subject "${subjectToDelete.name}" has been deleted successfully.`,
      });
      if (onSubjectDeleted) {
        onSubjectDeleted(subjectToDelete._id);
      }
    } catch (error) {
      console.error("Error deleting subject:", error);
      toast.error("Error", {
        description: error.message || "Failed to delete subject",
      });
    } finally {
      setOpenAlert(false);
      setSubjectToDelete(null);
    }
  };

  const confirmDelete = (e, subject) => {
    e.stopPropagation();
    setSubjectToDelete(subject);
    setOpenAlert(true);
  };

  return (
    <>
      <div
        className={
          isMobile ? "" : "hidden md:block w-80 border-r bg-background"
        }
      >
        <div className="px-4 h-full flex flex-col">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center gap-2">
              <Book className="h-5 w-5" />
              <h2 className="text-xl font-semibold">Subjects</h2>
            </div>
            <Button
              variant="ghost"
              size="icon"
              asChild
              aria-label="Add new subject"
            >
              <Link to="/dashboard/academics/subjects/create">
                <Plus className="h-5 w-5" />
              </Link>
            </Button>
          </div>
          <Input
            type="search"
            placeholder="Search subjects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="my-3"
            aria-label="Search subjects"
          />
          <ScrollArea className="flex-1">
            {isLoading ? (
              <div className="space-y-2">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="p-3 space-y-2">
                    <Skeleton className="h-5 w-3/4" />
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-4 w-1/4" />
                    </div>
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                ))}
              </div>
            ) : subjects.length > 0 ? (
              <ul className="space-y-1">
                {subjects.map((subject) => (
                  <li key={subject._id}>
                    <div className="relative group">
                      <button
                        className={`block w-full text-left p-3 rounded-lg transition ${
                          selectedSubject?._id === subject._id
                            ? "bg-primary/10"
                            : "hover:bg-muted"
                        }`}
                        onClick={() => onSelect(subject)}
                        aria-label={`Select ${subject.name}`}
                      >
                        <div className="flex-1 min-w-0">
                          <h3 className="text-base font-medium truncate">
                            {subject.name}
                          </h3>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                            <Book className="w-3 h-3 mr-1" />
                            <span>Code:</span>
                            {subject.code && (
                              <span className="px-1.5 py-0.5 bg-secondary/20 rounded text-secondary-foreground">
                                {subject.code}
                              </span>
                            )}
                          </div>
                          <div className="flex items-center gap-2 mt-2">
                            {subject.credit && (
                              <Badge variant="outline" className="text-xs">
                                <GraduationCap className="w-3 h-3 mr-1" />
                                {subject.credit} Credits
                              </Badge>
                            )}
                          </div>
                        </div>
                      </button>
                      <div className="absolute top-2 right-2 hidden group-hover:flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          asChild
                          aria-label="Edit subject"
                        >
                          <Link
                            to={`/dashboard/academics/subjects/${subject._id}/edit`}
                          >
                            <Pencil className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => confirmDelete(e, subject)}
                          aria-label="Delete subject"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <div className="flex justify-center p-4">No subjects found</div>
            )}
          </ScrollArea>
        </div>
      </div>

      <AlertDialog open={openAlert} onOpenChange={setOpenAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {subjectToDelete?.name}?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              subject and remove its data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Subject
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
