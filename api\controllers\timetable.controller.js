import Timetable from "../models/timetable.model.js";

// Helper function to clean timetable data
const cleanTimetableData = (timetableData) => {
  if (!timetableData.timetable) return timetableData;

  const cleanedData = { ...timetableData };

  // Clean each day's periods
  Object.keys(cleanedData.timetable).forEach((day) => {
    if (cleanedData.timetable[day]) {
      cleanedData.timetable[day] = cleanedData.timetable[day]
        .filter((period) => {
          return (
            period.startTime &&
            period.endTime &&
            period.subjectId &&
            period.subjectId !== "" &&
            period.teacherId &&
            period.teacherId !== ""
          );
        })
        .map((period) => {
          const cleanedPeriod = { ...period };

          if (!cleanedPeriod.subjectId || cleanedPeriod.subjectId === "") {
            delete cleanedPeriod.subjectId;
          }
          if (!cleanedPeriod.teacherId || cleanedPeriod.teacherId === "") {
            delete cleanedPeriod.teacherId;
          }

          return cleanedPeriod;
        });
    }
  });

  return cleanedData;
};

// Create a new timetable
export const createTimetable = async (req, res) => {
  try {
    let timetableData = req.body;

    // Clean the timetable data
    timetableData = cleanTimetableData(timetableData);

    if (!timetableData.schoolId && req.user?.schoolId) {
      timetableData.schoolId = req.user.schoolId;
    }

    // Set created by
    timetableData.createdBy = req.user._id;

    // Check for existing active timetable
    const existingTimetable = await Timetable.findOne({
      schoolId: timetableData.schoolId,
      classId: timetableData.classId,
      schoolId: req.user?.schoolId || null,
      termId: timetableData.termId,
      status: "active",
    });

    if (existingTimetable) {
      return res.status(400).json({
        success: false,
        message:
          "An active timetable already exists for this class-section-term combination",
      });
    }

    const newTimetable = new Timetable(timetableData);
    await newTimetable.save();

    // Populate the response
    await newTimetable.populate([
      { path: "classId", select: "name" },
      { path: "sectionId", select: "name" },
      { path: "termId", select: "name" },
      { path: "schoolId", select: "name" },
      { path: "createdBy", select: "name email" },
    ]);

    res.status(201).json({
      success: true,
      message: "Timetable created successfully",
      data: newTimetable,
    });
  } catch (error) {
    console.error("Create Timetable Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message:
          "Active timetable already exists for this class-section-term combination",
      });
    }

    res.status(500).json({
      success: false,
      message:
        error.message || "Internal server error. Please try again later.",
    });
  }
};

// Get all timetables
export const getAllTimetables = async (req, res) => {
  try {
    let query = {};

    if (req.user?.schoolId) {
      query.schoolId = req.user.schoolId;
    }

    const timetables = await Timetable.find(query)
      .sort({ createdAt: -1 })
      .populate([
        { path: "classId", select: "name" },
        { path: "sectionId", select: "name" },
        { path: "termId", select: "name" },
        { path: "schoolId", select: "name" },
        { path: "createdBy", select: "name email" },
        { path: "lastModifiedBy", select: "name email" },
      ]);

    res.status(200).json({
      success: true,
      count: timetables.length,
      data: timetables,
    });
  } catch (error) {
    console.error("Get All Timetables Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get timetable by ID
export const getTimetableById = async (req, res) => {
  try {
    const timetable = await Timetable.findById(req.params.id).populate([
      { path: "classId", select: "name" },
      { path: "sectionId", select: "name" },
      { path: "termId", select: "name" },
      { path: "schoolId", select: "name" },
      { path: "createdBy", select: "name email" },
      { path: "lastModifiedBy", select: "name email" },
    ]);

    if (!timetable) {
      return res.status(404).json({
        success: false,
        message: "Timetable not found",
      });
    }

    res.status(200).json({
      success: true,
      data: timetable,
    });
  } catch (error) {
    console.error("Get Timetable By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update timetable
export const updateTimetable = async (req, res) => {
  try {
    const timetableData = req.body;
    timetableData.lastModifiedBy = req.user._id;

    const timetable = await Timetable.findByIdAndUpdate(
      req.params.id,
      timetableData,
      {
        new: true,
        runValidators: true,
      }
    ).populate([
      { path: "classId", select: "name" },
      { path: "sectionId", select: "name" },
      { path: "termId", select: "name" },
      { path: "schoolId", select: "name" },
      { path: "lastModifiedBy", select: "name email" },
    ]);

    if (!timetable) {
      return res.status(404).json({
        success: false,
        message: "Timetable not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Timetable updated successfully",
      data: timetable,
    });
  } catch (error) {
    console.error("Update Timetable Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message:
          "Active timetable already exists for this class-section-term combination",
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || "Internal server error.",
    });
  }
};

// Delete timetable
export const deleteTimetable = async (req, res) => {
  try {
    const timetable = await Timetable.findByIdAndDelete(req.params.id);

    if (!timetable) {
      return res.status(404).json({
        success: false,
        message: "Timetable not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Timetable deleted successfully",
    });
  } catch (error) {
    console.error("Delete Timetable Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update timetable status
export const updateTimetableStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (!["active", "inactive"].includes(status)) {
      return res.status(400).json({
        success: false,
        message: "Invalid status. Must be active, or inactive",
      });
    }

    const timetable = await Timetable.findByIdAndUpdate(
      req.params.id,
      { status, lastModifiedBy: req.user._id },
      { new: true, runValidators: true }
    );

    if (!timetable) {
      return res.status(404).json({
        success: false,
        message: "Timetable not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Timetable status updated successfully",
      data: timetable,
    });
  } catch (error) {
    console.error("Update Timetable Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get timetable by class and section
export const getTimetableByClassSection = async (req, res) => {
  try {
    const { classId, sectionId } = req.params;

    let query = {
      classId,
      sectionId,
      status: "active",
      ...(req.user?.schoolId && { schoolId: req.user.schoolId }),
    };

    const timetable = await Timetable.findOne(query)
      .sort({ createdAt: -1 })
      .populate([
        { path: "classId", select: "name" },
        { path: "sectionId", select: "name" },
        { path: "termId", select: "name" },
      ]);

    if (!timetable) {
      return res.status(404).json({
        success: false,
        message: "No timetable found for this class and section",
      });
    }

    res.status(200).json({
      success: true,
      data: timetable,
    });
  } catch (error) {
    console.error("Get Timetable By Class Section Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get teacher's timetable
export const getTeacherTimetable = async (req, res) => {
  try {
    const { teacherId } = req.params;

    let query = {
      ...(req.user?.schoolId && { schoolId: req.user.schoolId }),
      status: "active",
    };

    const timetables = await Timetable.find(query).populate([
      { path: "classId", select: "name" },
      { path: "sectionId", select: "name" },
      { path: "termId", select: "name" },
    ]);

    // Filter timetables that contain the teacher
    const teacherTimetables = timetables.filter((timetable) => {
      const days = [
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
      ];
      return days.some((day) => {
        if (timetable.timetable && timetable.timetable[day]) {
          return timetable.timetable[day].some(
            (period) =>
              period.teacherId && period.teacherId.toString() === teacherId
          );
        }
        return false;
      });
    });

    res.status(200).json({
      success: true,
      count: teacherTimetables.length,
      data: teacherTimetables,
    });
  } catch (error) {
    console.error("Get Teacher Timetable Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
