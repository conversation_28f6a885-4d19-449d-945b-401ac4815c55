import axiosInstance from "./axios-instance";

// Create a new event
export const createEvent = async (eventData) => {
  try {
    const response = await axiosInstance.post("/events/create", eventData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating event:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create event");
  }
};

// Get all events with filters
export const getEvents = async (filters = {}) => {
  try {
    const queryParams = new URLSearchParams();
    const queryString = queryParams.toString();
    const url = queryString ? `/events?${queryString}` : "/events";

    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching events:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch events");
  }
};

// Get event by ID
export const getEventById = async (id) => {
  try {
    const response = await axiosInstance.get(`/events/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching event with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch event with ID ${id}`)
    );
  }
};

// Update event
export const updateEvent = async (id, eventData) => {
  try {
    const response = await axiosInstance.put(`/events/${id}`, eventData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating event with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to update event with ID ${id}`)
    );
  }
};

// Delete event
export const deleteEvent = async (id) => {
  try {
    const response = await axiosInstance.delete(`/events/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting event with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to delete event with ID ${id}`)
    );
  }
};

// Update event status (scheduled, in_progress, completed, cancelled, postponed)
export const updateEventStatus = async (id, status) => {
  try {
    const response = await axiosInstance.patch(`/events/${id}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating event status for event with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update event status for event with ID ${id}`)
    );
  }
};

// Update event active status (active/inactive)
export const updateEventActiveStatus = async (id, isActive) => {
  try {
    const response = await axiosInstance.patch(`/events/${id}/active-status`, {
      isActive,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating event active status for event with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update event active status for event with ID ${id}`)
    );
  }
};

// Get events by date range
export const getEventsByDateRange = async (startDate, endDate) => {
  try {
    const response = await axiosInstance.get(
      `/events/date-range/${startDate}/${endDate}`
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching events for date range ${startDate} to ${endDate}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(
        `Failed to fetch events for date range ${startDate} to ${endDate}`
      )
    );
  }
};

// Get upcoming events
export const getUpcomingEvents = async (limit = 5) => {
  try {
    const response = await axiosInstance.get(`/events/upcoming?limit=${limit}`);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching upcoming events:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch upcoming events");
  }
};

// Get events by type
export const getEventsByType = async (eventType) => {
  try {
    const response = await axiosInstance.get(`/events/type/${eventType}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching events of type ${eventType}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch events of type ${eventType}`)
    );
  }
};
