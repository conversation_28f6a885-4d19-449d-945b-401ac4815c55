import mongoose from "mongoose";

const TeacherSchema = new mongoose.Schema(
  {
    // Personal Information
    title: {
      type: String,
      required: [true, "Title is required"],
      trim: true,
    },
    firstName: {
      type: String,
      required: [true, "First name is required"],
      trim: true,
    },
    lastName: {
      type: String,
      required: [true, "Last name is required"],
      trim: true,
    },
    dateOfBirth: {
      type: Date,
      required: [true, "Date of birth is required"],
    },
    gender: {
      type: String,
      required: [true, "Gender is required"],
      trim: true,
    },
    bloodGroup: {
      type: String,
      trim: true,
    },
    maritalStatus: {
      type: String,
      trim: true,
    },
    nationality: {
      type: String,
      required: [true, "Nationality is required"],
      trim: true,
    },
    religion: {
      type: String,
      trim: true,
    },
    employeeId: {
      type: String,
      required: [true, "Employee ID is required"],
      trim: true,
      unique: true,
    },
    aadharNumber: {
      type: String,
      trim: true,
      validate: {
        validator: function (v) {
          return !v || /^\d{12}$/.test(v);
        },
        message: "Aadhar number must be 12 digits",
      },
    },
    panNumber: {
      type: String,
      trim: true,
      validate: {
        validator: function (v) {
          return !v || /^[A-Z]{5}\d{4}[A-Z]{1}$/.test(v);
        },
        message: "Invalid PAN number format",
      },
    },
    profilePhoto: {
      type: String,
      trim: true,
    },

    // Address Information
    address: {
      type: String,
      trim: true,
    },
    city: {
      type: String,
      required: [true, "City is required"],
      trim: true,
    },
    state: {
      type: String,
      required: [true, "State is required"],
      trim: true,
    },
    pincode: {
      type: String,
      required: [true, "Pincode is required"],
      trim: true,
      validate: {
        validator: function (v) {
          return /^\d{6}$/.test(v);
        },
        message: "Pincode must be 6 digits",
      },
    },
    country: {
      type: String,
      required: [true, "Country is required"],
      trim: true,
    },

    // Contact Information
    personalEmail: {
      type: String,
      required: [true, "Personal email is required"],
      trim: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    phone: {
      type: String,
      required: [true, "Phone number is required"],
      trim: true,
    },
    alternatePhone: {
      type: String,
      trim: true,
    },
    preferredContactMethod: {
      type: String,
      enum: ["email", "phone"],
      default: "email",
      trim: true,
    },

    // Professional Information
    designation: {
      type: String,
      required: [true, "Designation is required"],
      trim: true,
    },
    department: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Department",
      required: [true, "Department is required"],
    },
    primarySubject: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subject",
      required: [true, "Primary subject is required"],
    },
    secondarySubject: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subject",
      required: [true, "Secondary subject is required"],
    },
    qualification: {
      type: String,
      required: [true, "Qualification is required"],
      trim: true,
    },
    employmentType: {
      type: String,
      required: [true, "Employment type is required"],
      trim: true,
    },
    experience: {
      type: String,
      trim: true,
    },
    previousExperience: {
      type: String,
      trim: true,
    },

    // Emergency Contact
    emergencyContactName: {
      type: String,
      required: [true, "Emergency contact name is required"],
      trim: true,
    },
    emergencyContactPhone: {
      type: String,
      required: [true, "Emergency contact phone is required"],
      trim: true,
    },
    emergencyContactRelation: {
      type: String,
      required: [true, "Emergency contact relation is required"],
      trim: true,
    },

    // System Access & Permissions
    email: {
      type: String,
      required: [true, "Email is required"],
      trim: true,
      unique: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    password: {
      type: String,
      required: [true, "Password is required"],
      minlength: [8, "Password must be at least 8 characters"],
      select: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },

    // Bank Details
    bankName: {
      type: String,
      trim: true,
    },
    bankAccountNumber: {
      type: String,
      trim: true,
    },
    ifscCode: {
      type: String,
      trim: true,
      validate: {
        validator: function (v) {
          return !v || /^[A-Z]{4}0[A-Z0-9]{6}$/.test(v);
        },
        message: "Invalid IFSC code format",
      },
    },

    // Additional Information
    specializations: {
      type: String,
      trim: true,
    },
    achievements: {
      type: String,
      trim: true,
    },
    notes: {
      type: String,
      trim: true,
    },
    additionalNotes: {
      type: String,
      trim: true,
    },

    // Status
    status: {
      type: String,
      enum: ["active", "inactive", "on-leave"],
      default: "active",
    },

    // School Reference
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },

    // Classes and Subjects
    assignedClasses: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Class",
      },
    ],
  },
  {
    timestamps: true,
  }
);

export default mongoose.model("Teacher", TeacherSchema);
