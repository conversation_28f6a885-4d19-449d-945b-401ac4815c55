import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { TimetableForm } from "@/components/forms/dashboard/schedules/timetable-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useTimetable } from "@/context/timetable-context";
import { toast } from "sonner";

const CreateTimetable = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchTimetableById, isLoading } = useTimetable();
  const [timetableData, setTimetableData] = useState(null);
  const [loading, setLoading] = useState(!!id);

  useEffect(() => {
    const loadTimetableData = async () => {
      if (id) {
        try {
          const data = await fetchTimetableById(id);
          setTimetableData(data);
        } catch (error) {
          console.error("Failed to fetch timetable data:", error);
          toast.error("Failed to load timetable data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadTimetableData();
  }, [id, fetchTimetableById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Timetable" : "Create New Timetable"}
        actions={[
          {
            label: "Back to Timetables",
            href: "/dashboard/schedule/timetable",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Timetables", href: "/dashboard/schedule/timetable" },
          { label: id ? "Edit Timetable" : "Create Timetable" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <TimetableForm editingId={id} initialData={timetableData} />
      )}
    </Container>
  );
};

export default CreateTimetable;
