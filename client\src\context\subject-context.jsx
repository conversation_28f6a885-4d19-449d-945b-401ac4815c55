import { createContext, useContext, useState } from "react";
import {
  createSubject,
  getSubjects,
  getSubjectById,
  updateSubject,
  deleteSubject,
  updateSubjectStatus,
  getSubjectsByDepartment,
} from "@/api/subject-api";

const SubjectContext = createContext({
  subjects: [],
  currentSubject: null,
  isLoading: false,
  error: null,
  addSubject: () => {},
  fetchAllSubjects: () => {},
  fetchSubjectById: () => {},
  editSubject: () => {},
  removeSubject: () => {},
  updateStatus: () => {},
  fetchSubjectsByDepartment: () => {},
});

export const SubjectProvider = ({ children }) => {
  const [subjects, setSubjects] = useState([]);
  const [currentSubject, setCurrentSubject] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addSubject = async (subjectData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createSubject(subjectData);
      setSubjects((prevSubjects) => [...prevSubjects, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create subject");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllSubjects = async (filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSubjects(filters);
      setSubjects(response.data);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to fetch subjects");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchSubjectById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSubjectById(id);
      setCurrentSubject(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch subject with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editSubject = async (id, subjectData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateSubject(id, subjectData);
      setSubjects(
        subjects.map((subj) => (subj._id === id ? response.data : subj))
      );
      if (currentSubject && currentSubject._id === id) {
        setCurrentSubject(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update subject with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeSubject = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteSubject(id);
      setSubjects(subjects.filter((subj) => subj._id !== id));
      if (currentSubject && currentSubject._id === id) {
        setCurrentSubject(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete subject with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, isActive) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateSubjectStatus(id, isActive);
      setSubjects(
        subjects.map((subj) => (subj._id === id ? response.data : subj))
      );
      if (currentSubject && currentSubject._id === id) {
        setCurrentSubject(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(
        error.message || `Failed to update subject status with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  const fetchSubjectsByDepartment = async (departmentId) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSubjectsByDepartment(departmentId);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(
        error.message ||
          `Failed to fetch subjects for department: ${departmentId}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  // Create subject options for select inputs
  const subjectOptions = subjects.map((subj) => ({
    label: `${subj.name} (${subj.code})`,
    value: subj._id,
    id: subj._id,
    name: subj.name,
    code: subj.code,
    credit: subj.credit,
    semester: subj.semester,
  }));

  return (
    <SubjectContext.Provider
      value={{
        subjects,
        setSubjects,
        currentSubject,
        isLoading,
        error,
        addSubject,
        fetchAllSubjects,
        fetchSubjectById,
        editSubject,
        removeSubject,
        updateStatus,
        fetchSubjectsByDepartment,
        subjectOptions,
      }}
    >
      {children}
    </SubjectContext.Provider>
  );
};

export const useSubject = () => useContext(SubjectContext);
