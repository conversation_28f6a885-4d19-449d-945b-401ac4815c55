import express from "express";
import {
  createFee,
  getAllFees,
  getFeeById,
  updateFee,
  deleteFee,
  updateFeeStatus,
  getFeesByClass,
  getFeesByTerm,
} from "../controllers/fee.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createFee
);

router.get("/", protect, authorize(["admin", "school-admin"]), getAllFees);

router.get("/:id", protect, authorize(["admin", "school-admin"]), getFeeById);

router.put("/:id", protect, authorize(["admin", "school-admin"]), updateFee);

router.delete("/:id", protect, authorize(["admin", "school-admin"]), deleteFee);

router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateFeeStatus
);

router.get(
  "/class/:classId",
  protect,
  authorize(["admin", "school-admin"]),
  getFeesByClass
);

router.get(
  "/term/:termId",
  protect,
  authorize(["admin", "school-admin"]),
  getFeesByTerm
);

export default router;
