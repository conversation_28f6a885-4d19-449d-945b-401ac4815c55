import { createContext, useContext, useState } from "react";
import {
  createTerm,
  getTerms,
  getTermById,
  updateTerm,
  deleteTerm,
  updateTermStatus,
} from "@/api/term-api";

const TermContext = createContext({
  terms: [],
  currentTerm: null,
  isLoading: false,
  error: null,
  addTerm: () => {},
  fetchAllTerms: () => {},
  fetchTermById: () => {},
  editTerm: () => {},
  removeTerm: () => {},
  updateStatus: () => {},
});

export const TermProvider = ({ children }) => {
  const [terms, setTerms] = useState([]);
  const [currentTerm, setCurrentTerm] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addTerm = async (termData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createTerm(termData);
      setTerms((prevTerms) => [...prevTerms, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create term");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllTerms = async (filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getTerms(filters);
      setTerms(response.data);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to fetch terms");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchTermById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getTermById(id);
      setCurrentTerm(response.data);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || `Failed to fetch term with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editTerm = async (id, termData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateTerm(id, termData);
      setTerms(terms.map((term) => (term._id === id ? response.data : term)));
      if (currentTerm && currentTerm._id === id) {
        setCurrentTerm(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update term with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeTerm = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteTerm(id);
      setTerms(terms.filter((term) => term._id !== id));
      if (currentTerm && currentTerm._id === id) {
        setCurrentTerm(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete term with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, status) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateTermStatus(id, status);
      setTerms(terms.map((term) => (term._id === id ? response.data : term)));
      if (currentTerm && currentTerm._id === id) {
        setCurrentTerm(response.data);
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update term status with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  // Create term options for select inputs
  const termOptions = terms.map((term) => ({
    label: `${term.name} (${term.code})`,
    value: term._id,
    id: term._id,
    name: term.name,
    code: term.code,
    academicYear: term.academicYear,
    startDate: term.startDate,
    endDate: term.endDate,
  }));

  return (
    <TermContext.Provider
      value={{
        terms,
        setTerms,
        currentTerm,
        isLoading,
        error,
        addTerm,
        fetchAllTerms,
        fetchTermById,
        editTerm,
        removeTerm,
        updateStatus,
        termOptions,
      }}
    >
      {children}
    </TermContext.Provider>
  );
};

export const useTerm = () => useContext(TermContext);
